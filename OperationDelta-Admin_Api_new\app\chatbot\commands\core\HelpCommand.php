<?php

namespace app\chatbot\commands\core;

use app\chatbot\commands\AbstractCommand;
use app\chatbot\commands\CommandFactory;

/**
 * 帮助命令处理器
 * 显示所有可用命令的列表和帮助信息
 */
class HelpCommand extends AbstractCommand
{
    protected string $name = 'help';
    protected array $aliases = ['帮助', 'h', '?'];
    protected string $description = '显示帮助信息，列出所有可用命令';
    protected string $usage = '/help [命令名] - 显示指定命令的详细帮助';

    /**
     * 处理帮助命令
     * @param array $args 命令参数
     * @param array $context 执行上下文
     * @return array
     */
    protected function handle(array $args, array $context): array
    {
        // 如果指定了具体命令，显示该命令的详细帮助
        if (!empty($args)) {
            $commandName = strtolower(trim($args[0]));
            return $this->showCommandHelp($commandName, $context);
        }

        // 显示所有命令的列表
        return $this->showAllCommands($context);
    }

    /**
     * 显示所有命令列表
     * @param array $context 执行上下文
     * @return array
     */
    private function showAllCommands(array $context): array
    {
        $commandList = CommandFactory::getCommandList();
        $pluginCommands = $this->getPluginCommands();
        
        if (empty($commandList) && empty($pluginCommands)) {
            return $this->formatResponse(
                true,
                "暂无可用命令。",
                'text',
                [],
                $context
            );
        }

        $message = "📋 可用命令列表\n";

        // 显示核心命令
        if (!empty($commandList)) {
            $message .= "🔧 核心命令\n";
            foreach ($commandList as $name => $info) {
                $aliases = !empty($info['aliases']) ? ' (' . implode(', ', $info['aliases']) . ')' : '';
                $message .= "• /{$name}{$aliases} - {$info['description']}\n";
            }
        }

        // 显示插件命令（与核心命令统一格式）
        if (!empty($pluginCommands)) {
            $message .= "🔌 插件命令\n";
            foreach ($pluginCommands as $commands) {
                foreach ($commands as $command) {
                    $aliases = !empty($command['aliases']) ? ' (' . implode(', ', $command['aliases']) . ')' : '';
                    $message .= "• /{$command['name']}{$aliases} - {$command['description']}\n";
                }
            }
        }

        return $this->formatResponse(
            true,
            $message,
            'text',
            [],
            $context
        );
    }
    
    /**
     * 获取插件命令列表
     * @return array
     */
    private function getPluginCommands(): array
    {
        try {
            $enabledPlugins = \app\chatbot\plugins\manager\PluginManager::getEnabledPlugins();
            $pluginCommands = [];
            
            foreach ($enabledPlugins as $pluginName => $pluginInfo) {
                $commands = $this->getPluginCommandInfo($pluginName, $pluginInfo);
                if (!empty($commands)) {
                    $pluginCommands[$pluginName] = $commands;
                }
            }
            
            return $pluginCommands;
        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get plugin commands: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 获取插件的命令信息
     * @param string $pluginName 插件名称
     * @param array $pluginInfo 插件信息
     * @return array
     */
    private function getPluginCommandInfo(string $pluginName, array $pluginInfo): array
    {
        $commands = [];
        
        try {
            // 尝试从插件实例获取支持的命令
            $supportedCommands = $this->getPluginSupportedCommands($pluginName);
            
            if (!empty($supportedCommands)) {
                // 合并相同功能的命令
                $commands = $this->mergeSimilarCommands($pluginName, $supportedCommands);
            } else {
                // 如果没有获取到支持的命令，使用插件描述
                $description = isset($pluginInfo['description']) ? $pluginInfo['description'] : "插件 {$pluginName} 的功能";
                $commands[] = [
                    'name' => $pluginName,
                    'aliases' => [],
                    'description' => $description,
                    'usage' => "/{$pluginName} - 使用 {$description}"
                ];
            }
            
        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get plugin command info for {$pluginName}: " . $e->getMessage());
        }
        
        return $commands;
    }

    /**
     * 合并相同功能的命令
     * @param string $pluginName 插件名称
     * @param array $supportedCommands 支持的命令列表
     * @return array
     */
    private function mergeSimilarCommands(string $pluginName, array $supportedCommands): array
    {
        $commands = [];
        
        // 为特定插件定义命令分组
        switch ($pluginName) {
            case 'MapPasswordPlugin':
                // 密码相关命令合并为一组
                $passwordCommands = ['密码', '今日密码', '密码门', '地图密码'];
                $mainCommand = '密码';
                $aliases = array_diff($passwordCommands, [$mainCommand]);
                
                $commands[] = [
                    'name' => $mainCommand,
                    'aliases' => array_values($aliases),
                    'description' => '查询今日地图密码信息',
                    'usage' => "/{$mainCommand} - 查询今日地图密码信息"
                ];
                break;
                
            default:
                // 默认情况下，每个命令单独显示
                foreach ($supportedCommands as $command) {
                    $commands[] = [
                        'name' => $command,
                        'aliases' => [],
                        'description' => $this->getCommandDescription($pluginName, $command),
                        'usage' => "/{$command} - " . $this->getCommandDescription($pluginName, $command)
                    ];
                }
        }
        
        return $commands;
    }

    /**
     * 获取插件支持的命令列表
     * @param string $pluginName 插件名称
     * @return array
     */
    private function getPluginSupportedCommands(string $pluginName): array
    {
        try {
            // 尝试获取插件实例
            $pluginClass = "app\\chatbot\\plugins\\custom\\{$pluginName}";
            if (class_exists($pluginClass)) {
                $plugin = new $pluginClass();
                if (method_exists($plugin, 'getSupportedCommands')) {
                    return $plugin->getSupportedCommands();
                }
            }
        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get supported commands for plugin {$pluginName}: " . $e->getMessage());
        }
        
        return [];
    }

    /**
     * 获取命令描述
     * @param string $pluginName 插件名称
     * @param string $command 命令名称
     * @return string
     */
    private function getCommandDescription(string $pluginName, string $command): string
    {
        // 为特定插件和命令提供描述
        switch ($pluginName) {
            case 'MapPasswordPlugin':
                switch ($command) {
                    case '密码':
                    case '今日密码':
                    case '密码门':
                    case '地图密码':
                        return '查询今日地图密码信息';
                    default:
                        return '地图密码查询功能';
                }
            default:
                return "插件 {$pluginName} 的 {$command} 功能";
        }
    }

    /**
     * 显示指定命令的详细帮助
     * @param string $commandName 命令名称
     * @param array $context 执行上下文
     * @return array
     */
    private function showCommandHelp(string $commandName, array $context): array
    {
        $command = CommandFactory::getCommand($commandName);
        
        if (!$command) {
            return $this->formatResponse(
                false,
                "❌ 未找到命令：{$commandName}\n\n使用 /help 查看所有可用命令",
                'text',
                [],
                $context
            );
        }

        $help = $command->getHelp();
        
        // 添加使用示例
        $usage = $command->getUsage();
        if (!empty($usage)) {
            $help .= "\n\n📝 使用示例：\n{$usage}";
        }

        // 添加权限信息
        if ($command->hasPermission([])) {
            $help .= "\n\n🔐 权限：所有用户可用";
        } else {
            $help .= "\n\n🔐 权限：需要特殊权限";
        }

        return $this->formatResponse(
            true,
            $help,
            'text',
            [],
            $context
        );
    }

    /**
     * 验证命令参数
     * @param array $args 参数
     * @param array $context 上下文
     * @return array
     */
    protected function validateArgs(array $args, array $context): array
    {
        // help 命令最多接受一个参数（命令名）
        if (count($args) > 1) {
            return [
                'valid' => false,
                'message' => '❌ 参数过多。用法：/help [命令名]'
            ];
        }

        return ['valid' => true, 'message' => ''];
    }


}
