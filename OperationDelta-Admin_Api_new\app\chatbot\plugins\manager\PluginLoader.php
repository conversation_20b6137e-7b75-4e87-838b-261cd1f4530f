<?php

namespace app\chatbot\plugins\manager;

use app\chatbot\plugins\base\PluginInterface;
use think\facade\Log;

/**
 * 插件加载器
 * 负责插件的动态加载和热插拔功能
 */
class PluginLoader
{
    /**
     * 插件目录映射
     * @var array
     */
    private static array $pluginDirs = [];

    /**
     * 已加载的插件
     * @var array
     */
    private static array $loadedPlugins = [];

    /**
     * 插件文件监控
     * @var array
     */
    private static array $fileWatchers = [];

    /**
     * 初始化插件加载器
     */
    public static function initialize(): void
    {
        self::$pluginDirs = [
            'examples' => __DIR__ . '/../examples/',
            'custom' => __DIR__ . '/../custom/',
            'third_party' => __DIR__ . '/../third_party/'
        ];

        // 创建目录（如果不存在）
        foreach (self::$pluginDirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }

        Log::info('PluginLoader initialized');
    }

    /**
     * 扫描插件目录
     * @param string $dirName 目录名称
     * @return array 发现的插件列表
     */
    public static function scanDirectory(string $dirName = 'all'): array
    {
        $discoveredPlugins = [];

        if ($dirName === 'all') {
            foreach (self::$pluginDirs as $name => $dir) {
                $discoveredPlugins[$name] = self::scanSingleDirectory($dir);
            }
        } else {
            if (isset(self::$pluginDirs[$dirName])) {
                $discoveredPlugins[$dirName] = self::scanSingleDirectory(self::$pluginDirs[$dirName]);
            }
        }

        return $discoveredPlugins;
    }

    /**
     * 扫描单个目录
     * @param string $dir 目录路径
     * @return array
     */
    private static function scanSingleDirectory(string $dir): array
    {
        $plugins = [];

        if (!is_dir($dir)) {
            return $plugins;
        }

        $files = glob($dir . '*Plugin.php');
        foreach ($files as $file) {
            $pluginInfo = self::analyzePluginFile($file);
            if ($pluginInfo) {
                $plugins[] = $pluginInfo;
            }
        }

        return $plugins;
    }

    /**
     * 分析插件文件
     * @param string $filePath 文件路径
     * @return array|null
     */
    private static function analyzePluginFile(string $filePath): ?array
    {
        try {
            $content = file_get_contents($filePath);
            if (!$content) {
                return null;
            }

            // 提取类名
            if (preg_match('/class\s+(\w+Plugin)\s+extends/', $content, $matches)) {
                $className = $matches[1];
            } else {
                return null;
            }

            // 提取命名空间
            $namespace = '';
            if (preg_match('/namespace\s+([^;]+);/', $content, $matches)) {
                $namespace = $matches[1];
            }

            // 提取插件信息
            $pluginInfo = [
                'file_path' => $filePath,
                'class_name' => $className,
                'full_class_name' => $namespace ? $namespace . '\\' . $className : $className,
                'file_size' => filesize($filePath),
                'modified_time' => filemtime($filePath),
                'is_readable' => is_readable($filePath)
            ];

            // 尝试获取插件基本信息
            try {
                $fullClassName = $pluginInfo['full_class_name'];
                if (class_exists($fullClassName)) {
                    $plugin = new $fullClassName();
                    if ($plugin instanceof PluginInterface) {
                        $pluginInfo['name'] = $plugin->getName();
                        $pluginInfo['version'] = $plugin->getVersion();
                        $pluginInfo['description'] = $plugin->getDescription();
                        $pluginInfo['author'] = $plugin->getAuthor();
                        $pluginInfo['dependencies'] = $plugin->getDependencies();
                        $pluginInfo['status'] = $plugin->getStatus();
                    }
                }
            } catch (\Exception $e) {
                Log::warning("Failed to analyze plugin {$fullClassName}: " . $e->getMessage());
            }

            return $pluginInfo;

        } catch (\Exception $e) {
            Log::error("Failed to analyze plugin file {$filePath}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 动态加载插件
     * @param string $filePath 插件文件路径
     * @return bool 是否加载成功
     */
    public static function loadPlugin(string $filePath): bool
    {
        try {
            if (!file_exists($filePath)) {
                Log::error("Plugin file not found: {$filePath}");
                return false;
            }

            $pluginInfo = self::analyzePluginFile($filePath);
            if (!$pluginInfo) {
                Log::error("Failed to analyze plugin file: {$filePath}");
                return false;
            }

            $fullClassName = $pluginInfo['full_class_name'];
            
            // 检查类是否已存在
            if (!class_exists($fullClassName)) {
                require_once $filePath;
            }

            // 验证插件类
            if (!class_exists($fullClassName)) {
                Log::error("Plugin class not found after loading: {$fullClassName}");
                return false;
            }

            $plugin = new $fullClassName();
            if (!$plugin instanceof PluginInterface) {
                Log::error("Plugin class must implement PluginInterface: {$fullClassName}");
                return false;
            }

            // 注册到插件管理器
            $result = PluginManager::registerPlugin($fullClassName);
            if ($result) {
                self::$loadedPlugins[$plugin->getName()] = $pluginInfo;
                Log::info("Plugin loaded successfully: {$plugin->getName()}");
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("Failed to load plugin {$filePath}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 热加载插件
     * @param string $dirName 目录名称
     * @return array 加载结果
     */
    public static function hotLoad(string $dirName = 'all'): array
    {
        $results = [];
        $discoveredPlugins = self::scanDirectory($dirName);

        foreach ($discoveredPlugins as $dir => $plugins) {
            foreach ($plugins as $pluginInfo) {
                $filePath = $pluginInfo['file_path'];
                $pluginName = $pluginInfo['name'] ?? basename($filePath, '.php');
                
                // 检查是否已加载
                if (isset(self::$loadedPlugins[$pluginName])) {
                    $loadedInfo = self::$loadedPlugins[$pluginName];
                    
                    // 检查文件是否被修改
                    if ($pluginInfo['modified_time'] > $loadedInfo['modified_time']) {
                        Log::info("Plugin file modified, reloading: {$pluginName}");
                        self::unloadPlugin($pluginName);
                        $results[$pluginName] = self::loadPlugin($filePath);
                    } else {
                        $results[$pluginName] = true; // 已加载且未修改
                    }
                } else {
                    // 新插件，加载它
                    $results[$pluginName] = self::loadPlugin($filePath);
                }
            }
        }

        return $results;
    }

    /**
     * 卸载插件
     * @param string $pluginName 插件名称
     * @return bool 是否卸载成功
     */
    public static function unloadPlugin(string $pluginName): bool
    {
        $result = PluginManager::uninstallPlugin($pluginName);
        if ($result) {
            unset(self::$loadedPlugins[$pluginName]);
            Log::info("Plugin unloaded: {$pluginName}");
        }
        return $result;
    }

    /**
     * 获取已加载的插件列表
     * @return array
     */
    public static function getLoadedPlugins(): array
    {
        return self::$loadedPlugins;
    }

    /**
     * 启动文件监控
     * @param string $dirName 目录名称
     * @param int $interval 检查间隔（秒）
     */
    public static function startFileWatcher(string $dirName = 'all', int $interval = 30): void
    {
        if (isset(self::$fileWatchers[$dirName])) {
            Log::warning("File watcher already running for directory: {$dirName}");
            return;
        }

        self::$fileWatchers[$dirName] = [
            'dir' => $dirName,
            'interval' => $interval,
            'last_check' => time(),
            'running' => true
        ];

        Log::info("File watcher started for directory: {$dirName}");
    }

    /**
     * 停止文件监控
     * @param string $dirName 目录名称
     */
    public static function stopFileWatcher(string $dirName = 'all'): void
    {
        if (isset(self::$fileWatchers[$dirName])) {
            self::$fileWatchers[$dirName]['running'] = false;
            unset(self::$fileWatchers[$dirName]);
            Log::info("File watcher stopped for directory: {$dirName}");
        }
    }

    /**
     * 检查文件变化
     * @return array 变化的文件列表
     */
    public static function checkFileChanges(): array
    {
        $changes = [];

        foreach (self::$fileWatchers as $dirName => $watcher) {
            if (!$watcher['running']) {
                continue;
            }

            $currentTime = time();
            if ($currentTime - $watcher['last_check'] >= $watcher['interval']) {
                $discoveredPlugins = self::scanDirectory($dirName);
                
                foreach ($discoveredPlugins as $dir => $plugins) {
                    foreach ($plugins as $pluginInfo) {
                        $pluginName = $pluginInfo['name'] ?? basename($pluginInfo['file_path'], '.php');
                        
                        if (isset(self::$loadedPlugins[$pluginName])) {
                            $loadedInfo = self::$loadedPlugins[$pluginName];
                            
                            if ($pluginInfo['modified_time'] > $loadedInfo['modified_time']) {
                                $changes[] = [
                                    'plugin_name' => $pluginName,
                                    'file_path' => $pluginInfo['file_path'],
                                    'action' => 'modified',
                                    'timestamp' => $currentTime
                                ];
                            }
                        } else {
                            $changes[] = [
                                'plugin_name' => $pluginName,
                                'file_path' => $pluginInfo['file_path'],
                                'action' => 'new',
                                'timestamp' => $currentTime
                            ];
                        }
                    }
                }

                self::$fileWatchers[$dirName]['last_check'] = $currentTime;
            }
        }

        return $changes;
    }

    /**
     * 获取插件目录信息
     * @return array
     */
    public static function getPluginDirectories(): array
    {
        $info = [];
        
        foreach (self::$pluginDirs as $name => $dir) {
            $info[$name] = [
                'path' => $dir,
                'exists' => is_dir($dir),
                'readable' => is_readable($dir),
                'writable' => is_writable($dir),
                'plugin_count' => count(self::scanSingleDirectory($dir))
            ];
        }

        return $info;
    }

    /**
     * 清理无效插件
     * @return array 清理结果
     */
    public static function cleanupInvalidPlugins(): array
    {
        $results = [];
        $loadedPlugins = self::getLoadedPlugins();

        foreach ($loadedPlugins as $pluginName => $pluginInfo) {
            if (!file_exists($pluginInfo['file_path'])) {
                Log::warning("Plugin file not found, removing: {$pluginName}");
                self::unloadPlugin($pluginName);
                $results[$pluginName] = 'removed';
            } elseif (!is_readable($pluginInfo['file_path'])) {
                Log::warning("Plugin file not readable, disabling: {$pluginName}");
                PluginManager::disablePlugin($pluginName);
                $results[$pluginName] = 'disabled';
            }
        }

        return $results;
    }
}
