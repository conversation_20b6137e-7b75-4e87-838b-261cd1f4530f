# Chatbot 模块文档

## 概述

Chatbot 模块是 OperationDelta 项目的核心聊天机器人组件，提供了一套完整的、可扩展的机器人命令处理系统。该模块采用模块化设计，支持命令系统和插件系统，能够轻松扩展新功能。

## 架构设计

### 核心组件

1. **命令系统 (Command System)**
   - 基于接口的设计，支持核心命令的快速开发
   - 命令工厂模式，统一管理所有命令
   - 支持命令别名、权限控制和帮助信息

2. **插件系统 (Plugin System)**
   - 动态加载和热插拔支持
   - 插件依赖管理
   - 插件生命周期管理（初始化、启用、禁用、卸载）

3. **控制器层 (Controller Layer)**
   - 统一的API接口
   - 请求处理和响应格式化
   - 用户和群组管理

## 目录结构

```
app/chatbot/
├── README.md                    # 本文档
├── controller/                  # 控制器层
│   └── ChatbotController.php    # 主控制器
├── commands/                    # 命令系统
│   ├── CommandInterface.php     # 命令接口
│   ├── AbstractCommand.php      # 命令抽象类
│   ├── CommandFactory.php       # 命令工厂
│   └── core/                    # 核心命令
│       ├── HelpCommand.php      # 帮助命令
│       ├── PingCommand.php      # 心跳命令
│       ├── StatusCommand.php    # 状态命令
│       └── VersionCommand.php   # 版本命令
├── plugins/                     # 插件系统
│   ├── base/                    # 插件基础类
│   │   ├── PluginInterface.php  # 插件接口
│   │   └── AbstractPlugin.php   # 插件抽象类
│   ├── manager/                 # 插件管理
│   │   ├── PluginManager.php    # 插件管理器
│   │   └── PluginLoader.php     # 插件加载器
│   ├── examples/                # 示例插件
│   │   ├── WeatherPlugin.php    # 天气查询插件
│   │   ├── MusicPlugin.php      # 音乐播放插件
│   │   └── GamePlugin.php       # 游戏插件
│   ├── custom/                  # 自定义插件
│   └── third_party/             # 第三方插件
├── service/                     # 服务层
│   └── ChatbotDataService.php   # 数据服务
└── common/                      # 公共组件
    └── BaseController.php       # 基础控制器
```

## API 接口

### 健康检查

**GET** `/chatbot/health`

检查API服务状态

**响应示例：**
```json
{
    "code": 200,
    "message": "服务正常",
    "data": {
        "status": "ok",
        "timestamp": **********,
        "version": "1.0.0",
        "service": "chatbot-api"
    }
}
```

### 命令处理

**POST** `/chatbot/command/process`

处理机器人命令

**请求参数：**
```json
{
    "message": "/help",
    "platform": "kook",
    "user_id": "123456",
    "username": "用户名",
    "nickname": "昵称",
    "display_name": "显示名称",
    "avatar": "头像URL",
    "group_id": "789012",
    "group_name": "群组名称",
    "member_count": 100
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "命令执行成功",
    "data": {
        "success": true,
        "message": "📋 可用命令列表：\n\n• /help (帮助, h, ?)\n  显示帮助信息，列出所有可用命令\n\n• /ping (p)\n  测试连接状态\n\n💡 使用 /help [命令名] 查看具体命令的详细帮助",
        "type": "text",
        "timestamp": **********
    }
}
```

## 核心命令

### 内置命令列表

| 命令 | 别名 | 描述 | 权限 |
|------|------|------|------|
| `/help` | `帮助`, `h`, `?` | 显示帮助信息 | 所有用户 |
| `/ping` | `p` | 测试连接状态 | 所有用户 |
| `/status` | `状态` | 显示系统状态 | 所有用户 |
| `/version` | `版本`, `v` | 显示版本信息 | 所有用户 |

### 命令使用示例

```bash
# 显示所有命令
/help

# 显示特定命令帮助
/help ping

# 测试连接
/ping

# 查看系统状态
/status

# 查看版本信息
/version
```

## 插件系统

### 插件类型

1. **示例插件 (examples/)**
   - 天气查询插件 (`WeatherPlugin`)
   - 音乐播放插件 (`MusicPlugin`)
   - 游戏插件 (`GamePlugin`)

2. **自定义插件 (custom/)**
   - 用户自定义开发的插件

3. **第三方插件 (third_party/)**
   - 来自第三方的插件

### 插件命令示例

#### 天气查询插件
```bash
# 查询天气
/weather 北京

# 查看支持的城市
/cities
```

#### 音乐播放插件
```bash
# 搜索音乐
/music search 歌曲名

# 播放音乐
/music play 歌曲ID

# 暂停播放
/music pause

# 停止播放
/music stop

# 调整音量
/music volume 80

# 查看播放列表
/music playlist
```

#### 游戏插件
```bash
# 开始猜数字游戏
/game guess

# 开始石头剪刀布
/game rps

# 开始猜词游戏
/game word

# 结束当前游戏
/game endgame

# 查看可用游戏
/game games
```

## 开发指南

### 创建新命令

1. **创建命令类**

```php
<?php

namespace app\chatbot\commands\core;

use app\chatbot\commands\AbstractCommand;

class MyCommand extends AbstractCommand
{
    protected string $name = 'mycommand';
    protected array $aliases = ['mc', '我的命令'];
    protected string $description = '我的自定义命令';
    protected string $usage = '/mycommand [参数] - 命令使用说明';

    protected function handle(array $args, array $context): array
    {
        // 命令处理逻辑
        $result = "命令执行成功！";
        
        return $this->formatResponse(
            true,
            $result,
            'text'
        );
    }
}
```

2. **注册命令**

在 `CommandFactory.php` 中注册新命令：

```php
// 在 initialize() 方法中添加
self::$commands['mycommand'] = MyCommand::class;
```

### 创建新插件

1. **创建插件类**

```php
<?php

namespace app\chatbot\plugins\custom;

use app\chatbot\plugins\base\AbstractPlugin;

class MyPlugin extends AbstractPlugin
{
    protected string $name = 'myplugin';
    protected string $version = '1.0.0';
    protected string $description = '我的自定义插件';
    protected string $author = '开发者名称';
    protected array $dependencies = [];

    protected function onInitialize(): bool
    {
        // 插件初始化逻辑
        return true;
    }

    protected function onEnable(): bool
    {
        // 插件启用逻辑
        return true;
    }

    protected function onDisable(): bool
    {
        // 插件禁用逻辑
        return true;
    }

    protected function onUninstall(): bool
    {
        // 插件卸载逻辑
        return true;
    }

    public function handleCommand(string $command, string $args, array $context): ?array
    {
        // 处理插件命令
        if ($command === 'myplugin') {
            return [
                'success' => true,
                'message' => '插件命令执行成功！',
                'type' => 'text'
            ];
        }
        
        return null; // 不处理该命令
    }
}
```

2. **插件配置**

创建插件配置文件 `config.php`：

```php
<?php

return [
    'name' => 'myplugin',
    'version' => '1.0.0',
    'description' => '我的自定义插件',
    'author' => '开发者名称',
    'dependencies' => [],
    'enabled' => true
];
```

### 插件管理

#### 启用插件
```php
PluginManager::enablePlugin('myplugin');
```

#### 禁用插件
```php
PluginManager::disablePlugin('myplugin');
```

#### 获取插件列表
```php
$plugins = PluginManager::getPluginList();
```

#### 获取插件状态
```php
$status = PluginManager::getPluginStatus('myplugin');
```

## 最佳实践

### 命令开发

1. **命名规范**
   - 命令名称使用小写字母
   - 支持中文别名
   - 提供清晰的描述和使用说明

2. **错误处理**
   - 始终返回格式化的响应
   - 提供有意义的错误信息
   - 记录错误日志

3. **权限控制**
   - 实现 `hasPermission()` 方法
   - 检查用户权限
   - 提供权限不足的提示

### 插件开发

1. **生命周期管理**
   - 正确实现所有生命周期方法
   - 在初始化时检查依赖
   - 在卸载时清理资源

2. **配置管理**
   - 使用配置文件存储设置
   - 提供默认配置
   - 支持运行时配置更新

3. **错误处理**
   - 捕获并处理异常
   - 提供降级方案
   - 记录详细日志

### 性能优化

1. **缓存策略**
   - 缓存频繁访问的数据
   - 使用适当的缓存时间
   - 实现缓存失效机制

2. **资源管理**
   - 及时释放资源
   - 避免内存泄漏
   - 优化数据库查询

3. **异步处理**
   - 对于耗时操作使用异步处理
   - 避免阻塞主线程
   - 提供进度反馈

## 故障排除

### 常见问题

1. **命令未找到**
   - 检查命令是否正确注册
   - 确认命令名称拼写
   - 查看命令工厂初始化日志

2. **插件加载失败**
   - 检查插件依赖是否满足
   - 确认插件文件权限
   - 查看插件加载器日志

3. **权限不足**
   - 检查用户权限设置
   - 确认命令权限配置
   - 查看权限检查逻辑

### 调试技巧

1. **启用调试模式**
   - 设置日志级别为 DEBUG
   - 查看详细执行日志
   - 使用断点调试

2. **测试命令**
   - 使用 `/ping` 测试连接
   - 使用 `/status` 查看系统状态
   - 使用 `/help` 查看可用命令

3. **插件测试**
   - 创建测试插件
   - 验证插件生命周期
   - 测试插件命令处理

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础命令系统
- 实现插件系统
- 提供示例插件

## 贡献指南

1. **代码规范**
   - 遵循 PSR-4 自动加载规范
   - 使用中文注释
   - 保持代码整洁

2. **文档更新**
   - 更新相关文档
   - 提供使用示例
   - 说明变更内容

3. **测试验证**
   - 编写单元测试
   - 进行集成测试
   - 验证功能正常

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 联系方式

如有问题或建议，请联系开发团队。
