# 多平台机器人配置文件 - 用于配置MultiBot机器人的各项参数和平台连接信息

# 全局配置部分 - 定义整个应用程序的基本设置
global:
  # 应用程序名称，用于标识机器人实例
  app_name: MultiBot
  # 应用程序版本号，用于版本控制和更新管理
  app_version: 1.0.0

  # 云端API配置（可选）- 用于连接远程业务服务器
  # 云端API的身份验证令牌
  cloud_api_auth_token: test-token-123
  # 云端API的基础URL地址
  cloud_api_base_url: https://api.dfhub.cn/index.php/chatbot
  # 是否启用云端API功能
  cloud_api_enabled: false
  # 云端API请求超时时间（秒）
  cloud_api_timeout: 30
  # 云端API最大重试次数
  cloud_api_max_retries: 3

  # 命令前缀 - 定义用户触发机器人命令时需要使用的前缀符号
  command_prefixes:
  - /      # 斜杠前缀，如：/help
  - '!'    # 感叹号前缀，如：!ping
  - .      # 点号前缀，如：.status
  - '#'    # 井号前缀，如：#version

  # 调试模式开关，开启后会输出更详细的调试信息
  debug: false

  # 日志配置 - 控制应用程序日志的生成和管理
  # 日志文件存储路径和文件名
  log_file: logs/multibot.log
  # 日志级别，决定记录哪些级别的日志信息（DEBUG最详细，ERROR最紧急）
  log_level: INFO  # 可选值：DEBUG, INFO, WARNING, ERROR
  # 日志文件最大大小（字节），超过此大小将进行轮转
  log_max_size: 10485760  # 10MB
  # 日志文件备份数量，超过此数量将删除最旧的日志文件
  log_backup_count: 5

# 平台配置部分 - 定义各个聊天平台的连接参数
platforms:
  # KOOK平台配置 - 用于连接KOOK平台（原开黑啦）
  kook:
    config:
      # 是否启用消息压缩功能，可以减少网络传输数据量
      compress: true
      # KOOK Bot的访问令牌，用于身份验证
      # 获取方式：登录KOOK开发者平台 -> 创建应用 -> 获取Bot Token
      token: '1/MzkzNzg=/AvC1NdIt5m1QOj1zwHcw8w=='  # 请填入你的KOOK Bot Token
    # 是否启用KOOK平台支持
    enabled: true  # 改为true以启用KOOK平台

  # QQ平台配置（基于NapCat）- 用于连接QQ平台
  qq:
    config:
      # NapCat访问令牌，用于身份验证（可选，如果NapCat设置了token则需要填写）
      access_token: ''
      # NapCat服务的主机地址
      host: ************
      # NapCat服务的端口号
      port: 30599
    # 是否启用QQ平台支持
    enabled: true  # 当前已启用QQ平台
