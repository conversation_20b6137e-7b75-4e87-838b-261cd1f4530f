<?php

namespace app\chatbot\controller;

use app\chatbot\common\BaseController;
use app\chatbot\commands\CommandFactory;
use app\chatbot\plugins\manager\PluginManager;
use app\chatbot\plugins\manager\PluginLoader;
use think\facade\Config;
use think\Request;

/**
 * 多平台机器人主控制器
 * 提供基础的 API 端点，与 MultiBot 客户端对接
 */
class ChatbotController extends BaseController
{
    protected array $noNeedLogin = ['health'];

    /**
     * 健康检查端点
     * 路径：GET /chatbot/health
     * 功能：检查 API 服务状态
     */
    public function health(): void
    {
        $data = [
            'status' => 'ok',
            'timestamp' => time(),
            'version' => '1.0.0',
            'service' => 'chatbot-api'
        ];

        $this->success('服务正常', $data);
    }

    /**
     * 命令处理端点
     * 路径：POST /chatbot/command/process
     * 功能：处理机器人命令
     */
    public function process(Request $request): void
    {
        $startTime = microtime(true);
        $userRecord = null;
        $groupRecord = null;
        
        try {
            // 获取请求数据 - 支持JSON和表单数据
            $contentType = $request->header('content-type', '');
            $rawContent = $request->getContent();

            // 调试：记录原始请求数据
            \think\facade\Log::info('Chatbot raw content: ' . $rawContent);
            \think\facade\Log::info('Chatbot content-type: ' . $contentType);

            if (strpos($contentType, 'application/json') !== false && !empty($rawContent)) {
                // JSON请求体
                $input = json_decode($rawContent, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    \think\facade\Log::error('JSON decode error: ' . json_last_error_msg());
                    $input = [];
                }
            } else {
                // 表单数据
                $input = $request->param();
            }

            // 调试：记录解析后的数据
            \think\facade\Log::info('Chatbot process input: ' . json_encode($input));
            
            // 验证必要字段
            if (empty($input['message']) || empty($input['platform'])) {
                $this->error('缺少必要参数：message 和 platform');
                return;
            }

            // 提取命令信息
            $message = $input['message'];
            $platform = $input['platform'];
            $platformUserId = $input['user_id'] ?? '';
            $platformGroupId = $input['group_id'] ?? '';

            // 调试：记录提取的信息
            \think\facade\Log::info('Chatbot extracted: message=' . $message . ', platform=' . $platform);

            // 处理用户和群组记录
            if (!empty($platformUserId)) {
                $userRecord = $this->getOrCreateUser($platform, $platformUserId, [
                    'username' => $input['username'] ?? '',
                    'nickname' => $input['nickname'] ?? '',
                    'display_name' => $input['display_name'] ?? '',
                    'avatar' => $input['avatar'] ?? ''
                ]);
            }

            if (!empty($platformGroupId)) {
                $groupRecord = $this->getOrCreateGroup($platform, $platformGroupId, [
                    'group_name' => $input['group_name'] ?? '',
                    'member_count' => $input['member_count'] ?? 0
                ]);
            }

            // 处理命令
            $result = $this->processCommand($message, $platform, $platformUserId, $platformGroupId);

            // 计算执行时间
            $executionTime = (microtime(true) - $startTime) * 1000; // 转换为毫秒

            // 调试：记录处理结果
            \think\facade\Log::info('Chatbot process result: ' . json_encode($result));

            // 记录命令执行日志
            $this->logCommandExecution([
                'session_id' => $input['session_id'] ?? null,
                'platform' => $platform,
                'user_id' => $userRecord ? $userRecord->id : null,
                'group_id' => $groupRecord ? $groupRecord->id : null,
                'command_name' => $result['command_name'] ?? 'unknown',
                'command_args' => $result['command_args'] ?? [],
                'raw_message' => $message,
                'execution_status' => 'success',
                'execution_time_ms' => (int)$executionTime,
                'response_type' => $result['type'] ?? 'text',
                'response_preview' => mb_substr($result['reply'] ?? '', 0, 200),
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent', '')
            ]);

            // 更新统计信息
            if ($userRecord && $result['should_reply']) {
                $this->updateUserStats($userRecord->id, $result['command_name'] ?? 'unknown');
            }
            if ($groupRecord && $result['should_reply']) {
                $this->updateGroupStats($groupRecord->id);
            }

            // 返回结果
            $responseData = [
                'should_reply' => $result['should_reply'],
                'reply' => $result['reply'],
                'type' => $result['type']
            ];

            $this->success('处理成功', $responseData);

        } catch (\think\exception\HttpResponseException $e) {
            // HttpResponseException 应该直接抛出，不处理
            throw $e;
        } catch (\Exception $e) {
            // 计算执行时间
            $executionTime = (microtime(true) - $startTime) * 1000;
            
            // 记录错误日志
            $this->logCommandExecution([
                'session_id' => $input['session_id'] ?? null,
                'platform' => $platform ?? 'unknown',
                'user_id' => $userRecord ? $userRecord->id : null,
                'group_id' => $groupRecord ? $groupRecord->id : null,
                'command_name' => 'error',
                'command_args' => [],
                'raw_message' => $message ?? '',
                'execution_status' => 'error',
                'execution_time_ms' => (int)$executionTime,
                'response_type' => 'error',
                'response_preview' => '',
                'error_message' => $e->getMessage(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent', '')
            ]);

            // 调试：记录详细异常信息
            \think\facade\Log::error('Chatbot process error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
            \think\facade\Log::error('Chatbot process trace: ' . $e->getTraceAsString());
            
            $this->error('处理失败：' . $e->getMessage() . ' (File: ' . $e->getFile() . ':' . $e->getLine() . ')');
        }
    }

    /**
     * 配置获取端点
     * 路径：GET /chatbot/config
     * 功能：获取机器人配置信息
     */
    public function config(): void
    {
        $config = [
            'platforms' => [
                'qq' => [
                    'enabled' => Config::get('chatbot.platforms.qq.enabled', false),
                    'name' => 'QQ'
                ],
                'kook' => [
                    'enabled' => Config::get('chatbot.platforms.kook.enabled', false),
                    'name' => 'KOOK'
                ]
            ],
            'features' => [
                'command_prefixes' => Config::get('chatbot.command_prefixes', ['/', '!', '.', '#']),
                'max_message_length' => Config::get('chatbot.max_message_length', 1000)
            ],
            'version' => '1.0.0'
        ];

        $this->success('获取配置成功', $config);
    }

    /**
     * 处理命令的核心逻辑
     */
    private function processCommand(string $message, string $platform, string $userId, string $groupId): array
    {
        // 获取命令前缀
        $prefixes = Config::get('chatbot.command_prefixes', ['/', '!', '.', '#']);
        
        // 检查是否是命令
        $isCommand = false;
        $command = '';
        $args = '';

        foreach ($prefixes as $prefix) {
            if (strpos($message, $prefix) === 0) {
                $isCommand = true;
                $commandText = substr($message, strlen($prefix));
                $parts = explode(' ', $commandText, 2);
                $command = strtolower(trim($parts[0]));
                $args = isset($parts[1]) ? trim($args) : '';
                break;
            }
        }

        // 如果不是命令，不回复
        if (!$isCommand) {
            return [
                'should_reply' => false,
                'reply' => '',
                'type' => 'text',
                'command_name' => 'none',
                'command_args' => []
            ];
        }

        // 初始化命令工厂和插件管理器
        $this->initializeSystems();

        // 使用命令工厂处理命令
        try {
            $commandInstance = \app\chatbot\commands\CommandFactory::getCommand($command);
            
            if (!$commandInstance) {
                // 尝试通过插件处理命令
                $pluginResult = $this->processPluginCommand($command, $args, $platform, $userId, $groupId);
                if ($pluginResult) {
                    return $pluginResult;
                }

                return [
                    'should_reply' => true,
                    'reply' => "未知命令：{$command}。输入 /help 查看帮助。",
                    'type' => 'text',
                    'command_name' => $command,
                    'command_args' => []
                ];
            }

            // 构建执行上下文
            $context = [
                'platform' => $platform,
                'user_id' => $userId,
                'group_id' => $groupId,
                'user' => null, // 可以在这里添加用户对象
                'group' => null // 可以在这里添加群组对象
            ];

            // 解析参数
            $argsArray = !empty($args) ? explode(' ', $args) : [];

            // 执行命令
            $result = $commandInstance->execute($argsArray, $context);

            // 转换结果格式
            return [
                'should_reply' => $result['success'] ?? true,
                'reply' => $result['message'] ?? '',
                'type' => $result['type'] ?? 'text',
                'command_name' => $command,
                'command_args' => $argsArray
            ];

        } catch (\Exception $e) {
            \think\facade\Log::error('Command execution error: ' . $e->getMessage());
            
            return [
                'should_reply' => true,
                'reply' => '命令执行失败，请稍后重试。',
                'type' => 'text',
                'command_name' => $command,
                'command_args' => []
            ];
        }
    }

    /**
     * 初始化命令工厂和插件管理器
     */
    private function initializeSystems(): void
    {
        // 初始化命令工厂
        CommandFactory::initialize();
        
        // 初始化插件管理器
        PluginManager::initialize();
        PluginLoader::initialize();
        
        // 自动启用默认插件
        $this->enableDefaultPlugins();
    }
    
    /**
     * 启用默认插件
     */
    private function enableDefaultPlugins(): void
    {
        // 从配置文件读取需要自动启用的插件
        $configFile = __DIR__ . '/../config/plugins.php';
        if (file_exists($configFile)) {
            $config = include $configFile;
            $autoEnablePlugins = $config['auto_enable'] ?? [];
        } else {
            // 默认插件列表
            $autoEnablePlugins = ['weather', 'music', 'game'];
        }
        
        foreach ($autoEnablePlugins as $pluginName) {
            if (PluginManager::hasPlugin($pluginName)) {
                PluginManager::enablePlugin($pluginName);
                \think\facade\Log::info("Default plugin enabled: {$pluginName}");
            } else {
                \think\facade\Log::warning("Plugin not found: {$pluginName}");
            }
        }
    }

    /**
     * 处理插件命令
     * @param string $command 命令名称
     * @param string $args 命令参数
     * @param string $platform 平台
     * @param string $userId 用户ID
     * @param string $groupId 群组ID
     * @return array|null 处理结果，如果插件不支持该命令则返回null
     */
    private function processPluginCommand(string $command, string $args, string $platform, string $userId, string $groupId): ?array
    {
        try {
            // 获取已启用的插件
            $enabledPlugins = PluginManager::getEnabledPlugins();
            
            foreach ($enabledPlugins as $pluginName => $pluginInfo) {
                $plugin = PluginManager::getPlugin($pluginName);
                if (!$plugin) {
                    continue;
                }

                // 检查插件是否支持该命令
                if (method_exists($plugin, 'handleCommand')) {
                    $result = $plugin->handleCommand($command, $args, [
                        'platform' => $platform,
                        'user_id' => $userId,
                        'group_id' => $groupId
                    ]);
                    
                    if ($result && isset($result['handled']) && $result['handled']) {
                        return [
                            'should_reply' => $result['success'] ?? true,
                            'reply' => $result['message'] ?? '',
                            'type' => $result['type'] ?? 'text',
                            'command_name' => $command,
                            'command_args' => !empty($args) ? explode(' ', $args) : []
                        ];
                    }
                }
            }

            return null;

        } catch (\Exception $e) {
            \think\facade\Log::error('Plugin command processing error: ' . $e->getMessage());
            return null;
        }
    }



    /**
     * 用户统计端点
     * 路径：GET /chatbot/stats/users
     * 功能：获取用户统计数据
     */
    public function userStats(): void
    {
        try {
            $platform = $this->request->param('platform', 'qq');
            $days = (int)$this->request->param('days', 7);

            $stats = $this->dataService->getPlatformOverview($platform);
            $popularCommands = $this->dataService->getPopularCommands($platform, $days);

            $data = [
                'platform' => $platform,
                'overview' => $stats,
                'popular_commands' => $popularCommands,
                'period_days' => $days
            ];

            $this->success('获取用户统计成功', $data);
        } catch (\Exception $e) {
            \think\facade\Log::error('User stats error: ' . $e->getMessage());
            $this->error('获取用户统计失败：' . $e->getMessage());
        }
    }

    /**
     * 群组统计端点
     * 路径：GET /chatbot/stats/groups
     * 功能：获取群组统计数据
     */
    public function groupStats(): void
    {
        try {
            $platform = $this->request->param('platform', 'qq');
            
            // 获取群组统计
            $groups = \app\common\model\chatbot\Groups::where('platform', $platform)
                ->field('id, group_name, member_count, total_commands, active_users, last_activity_time, bot_status')
                ->order('total_commands', 'desc')
                ->limit(20)
                ->select()
                ->toArray();

            $data = [
                'platform' => $platform,
                'total_groups' => \app\common\model\chatbot\Groups::where('platform', $platform)->count(),
                'active_groups' => \app\common\model\chatbot\Groups::where('platform', $platform)
                    ->where('last_activity_time', '>=', time() - 7 * 24 * 3600)
                    ->count(),
                'top_groups' => $groups
            ];

            $this->success('获取群组统计成功', $data);
        } catch (\Exception $e) {
            \think\facade\Log::error('Group stats error: ' . $e->getMessage());
            $this->error('获取群组统计失败：' . $e->getMessage());
        }
    }

    /**
     * 命令统计端点
     * 路径：GET /chatbot/stats/commands
     * 功能：获取命令统计数据
     */
    public function commandStats(): void
    {
        try {
            $platform = $this->request->param('platform', 'qq');
            $days = (int)$this->request->param('days', 7);

            $popularCommands = $this->dataService->getPopularCommands($platform, $days);

            // 获取命令执行统计
            $startTime = time() - $days * 24 * 3600;
            $totalCommands = \app\common\model\chatbot\CommandLogs::where('platform', $platform)
                ->where('create_time', '>=', $startTime)
                ->count();
            
            $successCommands = \app\common\model\chatbot\CommandLogs::where('platform', $platform)
                ->where('create_time', '>=', $startTime)
                ->where('execution_status', 'success')
                ->count();

            $avgResponseTime = \app\common\model\chatbot\CommandLogs::where('platform', $platform)
                ->where('create_time', '>=', $startTime)
                ->where('execution_time_ms', '>', 0)
                ->avg('execution_time_ms') ?: 0;

            $data = [
                'platform' => $platform,
                'period_days' => $days,
                'total_commands' => $totalCommands,
                'success_commands' => $successCommands,
                'error_commands' => $totalCommands - $successCommands,
                'success_rate' => $totalCommands > 0 ? round(($successCommands / $totalCommands) * 100, 2) : 0,
                'avg_response_time_ms' => (int)$avgResponseTime,
                'popular_commands' => $popularCommands
            ];

            $this->success('获取命令统计成功', $data);
        } catch (\Exception $e) {
            \think\facade\Log::error('Command stats error: ' . $e->getMessage());
            $this->error('获取命令统计失败：' . $e->getMessage());
        }
    }

    /**
     * 系统概览端点
     * 路径：GET /chatbot/stats/overview
     * 功能：获取系统整体统计概览
     */
    public function overview(): void
    {
        try {
            $platform = $this->request->param('platform', 'qq');

            // 获取概览数据
            $overview = $this->dataService->getPlatformOverview($platform);

            // 生成今日统计（如果不存在）
            $this->dataService->generateDailyStats($platform);

            // 获取最新统计
            $todayStats = \app\common\model\chatbot\StatsDaily::where('stat_date', date('Y-m-d'))
                ->where('platform', $platform)
                ->find();

            $data = [
                'platform' => $platform,
                'overview' => $overview,
                'today_stats' => $todayStats ? $todayStats->toArray() : null,
                'system_info' => [
                    'version' => '1.0.0',
                    'uptime' => time(),
                    'memory_usage' => memory_get_usage(true),
                    'peak_memory' => memory_get_peak_usage(true)
                ]
            ];

            $this->success('获取系统概览成功', $data);
        } catch (\Exception $e) {
            \think\facade\Log::error('Overview error: ' . $e->getMessage());
            $this->error('获取系统概览失败：' . $e->getMessage());
        }
    }
}
