<?php

namespace app\chatbot\plugins\custom;

use app\chatbot\plugins\base\AbstractPlugin;
use app\api\service\MapPasswordService;
use think\facade\Log;

/**
 * 地图密码插件
 * 提供地图密码查询功能
 */
class MapPasswordPlugin extends AbstractPlugin
{
    /**
     * 插件名称
     */
    protected string $name = 'MapPasswordPlugin';

    /**
     * 插件版本
     */
    protected string $version = '1.0.0';

    /**
     * 插件描述
     */
    protected string $description = '地图密码查询插件，提供今日地图密码信息';

    /**
     * 插件作者
     */
    protected string $author = 'OperationDelta';

    /**
     * 插件依赖
     */
    protected array $dependencies = [];

    /**
     * 支持的命令关键词
     */
    private array $supportedCommands = [
        '密码',
        '今日密码',
        '密码门',
        '地图密码'
    ];

    /**
     * 地图密码服务实例
     */
    private ?MapPasswordService $passwordService = null;

    /**
     * 插件配置默认值
     */
    protected array $config = [
        'show_location' => true,
        'max_display_maps' => 8
    ];

    /**
     * 插件初始化回调
     * @return bool
     */
    protected function onInitialize(): bool
    {
        try {
            // 检查依赖
            if (!$this->checkDependencies()) {
                Log::error("MapPasswordPlugin dependencies check failed");
                return false;
            }

            // 初始化密码服务
            $this->passwordService = new MapPasswordService();
            
            // 验证配置
            $this->validateConfig();
            
            Log::info("MapPasswordPlugin initialized successfully with config: " . json_encode($this->config));
            return true;
        } catch (\Exception $e) {
            Log::error("MapPasswordPlugin initialization failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插件启用回调
     * @return bool
     */
    protected function onEnable(): bool
    {
        try {
            Log::info("MapPasswordPlugin enabled - supporting commands: " . implode(', ', $this->supportedCommands));
            return true;
        } catch (\Exception $e) {
            Log::error("MapPasswordPlugin enable failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插件禁用回调
     * @return bool
     */
    protected function onDisable(): bool
    {
        try {
            Log::info("MapPasswordPlugin disabled");
            return true;
        } catch (\Exception $e) {
            Log::error("MapPasswordPlugin disable failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 配置变更回调
     * @param array $config
     */
    protected function onConfigChanged(array $config): void
    {
        Log::info("MapPasswordPlugin config changed: " . json_encode($config));
        $this->validateConfig();
    }

    /**
     * 初始化密码服务
     * @return bool
     */
    private function initializePasswordService(): bool
    {
        try {
            if ($this->passwordService === null) {
                $this->passwordService = new MapPasswordService();
                Log::info("MapPasswordService initialized successfully");
            }
            return true;
        } catch (\Exception $e) {
            Log::error("MapPasswordService initialization failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理命令
     * @param string $command 命令名称
     * @param string $args 命令参数
     * @param array $context 执行上下文
     * @return array|null 处理结果
     */
    public function handleCommand(string $command, string $args, array $context): ?array
    {
        // 检查是否是支持的密码命令
        if (!in_array($command, $this->supportedCommands)) {
            return null;
        }

        // 验证参数
        $validation = $this->validateArgs($args, $context);
        if (!$validation['valid']) {
            return $this->formatErrorResponse($validation['message'], 'Invalid arguments', $context);
        }

        try {
            // 初始化密码服务
            if (!$this->initializePasswordService()) {
                return $this->formatErrorResponse('密码服务初始化失败，请稍后重试', 'MapPasswordService not available', $context);
            }

            // 获取密码数据
            $passwordData = $this->passwordService->getMapPasswordsWithCache();
            
            // 验证数据格式
            if (!is_array($passwordData)) {
                Log::error("MapPasswordPlugin: Invalid data type returned from service. Type: " . gettype($passwordData));
                return $this->formatErrorResponse('密码数据格式错误，请稍后重试', 'Invalid data type: ' . gettype($passwordData), $context);
            }
            
            // 记录成功信息
            Log::info("MapPasswordPlugin: Successfully retrieved password data for command: {$command}");
            
            // 格式化并返回响应
            return $this->formatPasswordResponse($passwordData, $context);

        } catch (\Exception $e) {
            Log::error("MapPasswordPlugin command failed: " . $e->getMessage());
            return $this->formatErrorResponse('获取密码信息失败，请稍后重试', $e->getMessage(), $context);
        }
    }

    /**
     * 验证命令参数
     * @param string $args 命令参数
     * @param array $context 执行上下文
     * @return array
     */
    private function validateArgs(string $args, array $context): array
    {
        // 密码命令可以接受可选的参数，但目前不需要特殊验证
        // 可以在这里添加参数验证逻辑，比如：
        // - 检查特定地图名称
        // - 验证日期格式
        // - 检查权限等
        
        return ['valid' => true, 'message' => ''];
    }

    /**
     * 格式化错误响应
     * @param string $message 用户友好的错误消息
     * @param string $error 技术错误信息
     * @param array $context 执行上下文（用于平台检测）
     * @return array
     */
    private function formatErrorResponse(string $message, string $error, array $context = []): array
    {
        // 根据平台格式化错误消息
        $platform = $context['platform'] ?? 'qq';
        $formattedMessage = $this->formatMessageForPlatform($message, $platform);
        
        return [
            'handled' => true,
            'success' => false,
            'message' => $formattedMessage,
            'type' => $platform === 'kook' ? 'kmarkdown' : 'text',
            'platform' => $platform,
            'error' => $error,
            'data' => null
        ];
    }

    /**
     * 验证插件配置
     */
    private function validateConfig(): void
    {
        if (!isset($this->config['show_location'])) {
            $this->config['show_location'] = true;
        }
        if (!isset($this->config['max_display_maps']) || $this->config['max_display_maps'] < 1) {
            $this->config['max_display_maps'] = 8;
        }
    }

    /**
     * 格式化密码响应（紧凑版）
     * @param array $passwordData 密码数据
     * @param array $context 执行上下文（用于平台检测）
     * @return array 格式化后的响应
     */
    private function formatPasswordResponse(array $passwordData, array $context = []): array
    {
        // 安全地获取和转换数据
        $title = $this->safeString($passwordData['title'] ?? '每日彩蛋门密码');
        $updateTime = $this->safeString($passwordData['update_time'] ?? date('Y-m-d H:i:s'));
        $totalMaps = $this->safeInt($passwordData['total_maps'] ?? 0);
        $passwords = $this->safeArray($passwordData['passwords'] ?? []);

        // 构建美观的密码信息文本
        $passwordText = "🔐 {$title}\n";
        $passwordText .= "📅 更新时间：{$updateTime}\n";
        $passwordText .= "🗺️ 地图总数：{$totalMaps}个\n\n";

        if (!empty($passwords)) {
            $passwordText .= "📋 地图密码列表\n";
            $displayCount = 0;
            $maxDisplay = min($this->config['max_display_maps'], 8);

            foreach ($passwords as $index => $passwordItem) {
                if ($displayCount >= $maxDisplay) {
                    $remaining = count($passwords) - $maxDisplay;
                    $passwordText .= "💡 还有 {$remaining} 个地图密码未显示\n";
                    break;
                }

                try {
                    // 解析密码项
                    $passwordData = is_string($passwordItem) ? json_decode($passwordItem, true) : $passwordItem;

                    if (is_array($passwordData)) {
                        $mapName = $this->safeString($passwordData['map'] ?? "地图" . ($index + 1));
                        $password = $this->safeString($passwordData['password'] ?? '未知');
                        $location = $this->safeString($passwordData['location'] ?? '');

                        // 美观显示
                        $passwordText .= "• {$mapName}：{$password}";
                        if ($this->config['show_location'] && !empty($location)) {
                            $passwordText .= "\n  📍 {$location}";
                        }
                        $passwordText .= "\n";
                        $displayCount++;
                    } else {
                        $safePassword = $this->safeString($passwordItem);
                        $passwordText .= "• 地图" . ($index + 1) . "：{$safePassword}\n";
                        $displayCount++;
                    }
                } catch (\Exception $e) {
                    Log::error("MapPasswordPlugin: Error processing password item {$index}: " . $e->getMessage());
                    $safePassword = $this->safeString($passwordItem);
                    $passwordText .= "• 地图" . ($index + 1) . "：{$safePassword}\n";
                    $displayCount++;
                }
            }
        } else {
            $passwordText .= "⚠️ 暂无密码数据";
        }

        // 根据平台格式化消息并添加全局结束提示
        $platform = $context['platform'] ?? 'qq';
        $formattedMessage = $this->formatMessageForPlatform($passwordText, $platform);

        // 添加全局结束提示
        $endingMessage = $this->getEndingMessage($platform);
        $formattedMessage .= $endingMessage;

        return [
            'handled' => true,
            'success' => true,
            'message' => $formattedMessage,
            'type' => $platform === 'kook' ? 'kmarkdown' : 'text',
            'platform' => $platform,
            'data' => [
                'title' => $title,
                'update_time' => $updateTime,
                'total_maps' => $totalMaps,
                'passwords' => $passwords
            ]
        ];
    }

    /**
     * 获取插件支持的命令列表
     * @return array 支持的命令列表
     */
    public function getSupportedCommands(): array
    {
        return $this->supportedCommands;
    }

    /**
     * 安全地转换为字符串
     */
    private function safeString($value): string
    {
        return is_string($value) ? $value : (string)$value;
    }

    /**
     * 安全地转换为整数
     */
    private function safeInt($value): int
    {
        return is_numeric($value) ? (int)$value : 0;
    }

    /**
     * 安全地转换为数组
     */
    private function safeArray($value): array
    {
        return is_array($value) ? $value : [];
    }

    /**
     * 根据平台格式化消息
     * @param string $message 原始消息
     * @param string $platform 平台名称
     * @return string 格式化后的消息
     */
    private function formatMessageForPlatform(string $message, string $platform): string
    {
        if ($platform === 'kook') {
            return $this->formatForKook($message);
        } else {
            return $this->formatForQQ($message);
        }
    }

    /**
     * 为 QQ 平台格式化消息（纯文本）
     */
    private function formatForQQ(string $message): string
    {
        // 移除 Markdown 语法
        return preg_replace(['/\*\*(.*?)\*\*/', '/`(.*?)`/'], ['$1', '$1'], $message);
    }

    /**
     * 为 KOOK 平台格式化消息（KMarkdown）
     * @param string $message 原始消息
     * @return string
     */
    private function formatForKook(string $message): string
    {
        $lines = explode("\n", $message);
        $formatted = [];

        foreach ($lines as $line) {
            $trimmed = trim($line);
            if (empty($trimmed)) {
                $formatted[] = '';
                continue;
            }

            // 处理主标题
            if (preg_match('/^🔐\s+(.+)$/', $trimmed, $matches)) {
                $formatted[] = "# 🔐 {$matches[1]}";
                continue;
            }

            // 处理更新时间
            if (preg_match('/^📅\s+更新时间：(.+)$/', $trimmed, $matches)) {
                $formatted[] = "**📅 更新时间：** `{$matches[1]}`";
                continue;
            }

            // 处理地图总数
            if (preg_match('/^�️\s+地图总数：(.+)$/', $trimmed, $matches)) {
                $formatted[] = "**�️ 地图总数：** `{$matches[1]}`";
                continue;
            }

            // 处理列表标题
            if (preg_match('/^📋\s+地图密码列表$/', $trimmed)) {
                $formatted[] = "## 📋 地图密码列表";
                continue;
            }

            // 处理地图密码项
            if (preg_match('/^•\s+(.+?)：(.+)$/', $trimmed, $matches)) {
                $mapName = $matches[1];
                $password = $matches[2];
                $formatted[] = "- **{$mapName}**：`{$password}`";
                continue;
            }

            // 处理位置信息
            if (preg_match('/^\s+📍\s+(.+)$/', $trimmed, $matches)) {
                $formatted[] = "  📍 *{$matches[1]}*";
                continue;
            }

            // 处理更多地图提示
            if (preg_match('/^💡\s+还有\s+(\d+)\s+个地图密码未显示$/', $trimmed, $matches)) {
                $formatted[] = "> 💡 还有 **{$matches[1]}** 个地图密码未显示";
                continue;
            }

            // 处理暂无数据
            if (preg_match('/^⚠️\s+暂无密码数据$/', $trimmed)) {
                $formatted[] = "> ⚠️ **暂无密码数据**";
                continue;
            }

            // 其他行保持原样
            $formatted[] = $trimmed;
        }

        // 在主要段落之间添加分隔线
        $result = implode("\n", $formatted);
        $result = preg_replace('/(\n## .+\n)/', "\n---$1", $result);
        $result = preg_replace('/^---\n/', '', $result); // 移除开头的分隔线

        return $result;
    }

    /**
     * 获取结束提示信息
     * @param string $platform 平台名称
     * @return string 结束提示
     */
    private function getEndingMessage(string $platform): string
    {
        try {
            // 加载插件配置文件
            $configPath = __DIR__ . '/../../config/plugins.php';
            if (!file_exists($configPath)) {
                return '';
            }

            $config = require $configPath;
            $endingMessages = $config['ending_messages'] ?? [];

            // 获取插件类名
            $className = 'MapPasswordPlugin';

            // 先查找插件专属配置
            if (isset($endingMessages['plugins'][$className][$platform])) {
                $message = $endingMessages['plugins'][$className][$platform];
                return $this->formatEndingMessageForPlatform($message, $platform);
            }

            // 如果没有插件专属配置，使用全局默认配置
            if (isset($endingMessages['default'][$platform])) {
                $message = $endingMessages['default'][$platform];
                return $this->formatEndingMessageForPlatform($message, $platform);
            }

            return '';
        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to load custom ending message: " . $e->getMessage());
            return '';
        }
    }

    /**
     * 格式化自定义结束提示语
     * @param string $message 自定义提示语
     * @param string $platform 平台名称
     * @return string 格式化后的提示语
     */
    private function formatEndingMessageForPlatform(string $message, string $platform): string
    {
        if ($platform === 'kook') {
            return "\n\n---\n*{$message}*";
        } else {
            return "\n\n{$message}";
        }
    }

    /**
     * 获取插件信息
     */
    public function getInfo(): array
    {
        $info = parent::getInfo();
        $info['supported_commands'] = $this->supportedCommands;
        return $info;
    }
}
