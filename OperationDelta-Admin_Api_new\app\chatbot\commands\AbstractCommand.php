<?php

namespace app\chatbot\commands;

use think\facade\Log;

/**
 * 抽象命令基类
 * 提供通用的命令处理逻辑和默认实现
 */
abstract class AbstractCommand implements CommandInterface
{
    /**
     * 命令名称
     * @var string
     */
    protected string $name = '';

    /**
     * 命令别名
     * @var array
     */
    protected array $aliases = [];

    /**
     * 命令描述
     * @var string
     */
    protected string $description = '';

    /**
     * 使用示例
     * @var string
     */
    protected string $usage = '';

    /**
     * 是否需要权限检查
     * @var bool
     */
    protected bool $requirePermission = false;

    /**
     * 获取命令名称
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * 获取命令别名列表
     * @return array
     */
    public function getAliases(): array
    {
        return $this->aliases;
    }

    /**
     * 获取命令描述
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * 获取命令使用示例
     * @return string
     */
    public function getUsage(): string
    {
        return $this->usage;
    }

    /**
     * 检查用户是否有权限执行此命令
     * @param array $context 执行上下文
     * @return bool
     */
    public function hasPermission(array $context): bool
    {
        if (!$this->requirePermission) {
            return true;
        }

        // 默认权限检查逻辑
        $user = $context['user'] ?? null;
        $group = $context['group'] ?? null;
        $platform = $context['platform'] ?? '';

        // 这里可以添加具体的权限检查逻辑
        // 例如：检查用户角色、群组权限等
        
        return true; // 默认允许执行
    }

    /**
     * 执行命令（模板方法模式）
     * @param array $args 命令参数
     * @param array $context 执行上下文
     * @return array
     */
    public function execute(array $args, array $context): array
    {
        try {
            // 权限检查
            if (!$this->hasPermission($context)) {
                return [
                    'success' => false,
                    'message' => '权限不足，无法执行此命令',
                    'type' => 'text'
                ];
            }

            // 参数验证
            $validationResult = $this->validateArgs($args, $context);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'message' => $validationResult['message'],
                    'type' => 'text'
                ];
            }

            // 执行具体命令逻辑
            $result = $this->handle($args, $context);

            // 记录执行日志
            $this->logExecution($args, $context, $result);

            return $result;

        } catch (\Exception $e) {
            Log::error("Command execution error: " . $e->getMessage(), [
                'command' => $this->getName(),
                'args' => $args,
                'context' => $context,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '命令执行失败：' . $e->getMessage(),
                'type' => 'text'
            ];
        }
    }

    /**
     * 获取命令帮助信息
     * @return string
     */
    public function getHelp(): string
    {
        $help = "命令：{$this->name}\n";
        $help .= "描述：{$this->description}\n";
        
        if (!empty($this->aliases)) {
            $help .= "别名：" . implode(', ', $this->aliases) . "\n";
        }
        
        if (!empty($this->usage)) {
            $help .= "用法：{$this->usage}\n";
        }

        return $help;
    }

    /**
     * 验证命令参数
     * @param array $args 参数
     * @param array $context 上下文
     * @return array ['valid' => bool, 'message' => string]
     */
    protected function validateArgs(array $args, array $context): array
    {
        // 子类可以重写此方法进行参数验证
        return ['valid' => true, 'message' => ''];
    }

    /**
     * 处理命令逻辑（子类必须实现）
     * @param array $args 命令参数
     * @param array $context 执行上下文
     * @return array 执行结果
     */
    abstract protected function handle(array $args, array $context): array;

    /**
     * 记录命令执行日志
     * @param array $args 参数
     * @param array $context 上下文
     * @param array $result 结果
     */
    protected function logExecution(array $args, array $context, array $result): void
    {
        Log::info("Command executed", [
            'command' => $this->getName(),
            'args' => $args,
            'platform' => $context['platform'] ?? '',
            'user_id' => $context['user_id'] ?? '',
            'group_id' => $context['group_id'] ?? '',
            'success' => $result['success'] ?? false,
            'execution_time' => microtime(true)
        ]);
    }

    /**
     * 格式化响应结果
     * @param bool $success 是否成功
     * @param string $message 消息
     * @param string $type 响应类型
     * @param array $data 额外数据
     * @param array $context 执行上下文（用于平台检测）
     * @return array
     */
    protected function formatResponse(bool $success, string $message, string $type = 'text', array $data = [], array $context = []): array
    {
        $platform = $context['platform'] ?? 'qq';
        
        // 根据平台格式化消息
        if ($platform === 'kook') {
            $formattedMessage = $this->formatForKook($message);
            $type = 'kmarkdown';
        } else {
            $formattedMessage = $this->formatForQQ($message);
            $type = 'text';
        }
        
        // 添加统一的结束提示（从配置读取或使用默认）
        $endingMessage = $this->getEndingMessage($platform);
        $formattedMessage .= $endingMessage;
        
        return array_merge([
            'success' => $success,
            'message' => $formattedMessage,
            'type' => $type,
            'platform' => $platform
        ], $data);
    }

    /**
     * 为 QQ 平台格式化消息（纯文本）
     * @param string $message 原始消息
     * @return string
     */
    protected function formatForQQ(string $message): string
    {
        // QQ 平台使用纯文本格式
        // 移除 Markdown 语法，保留基本格式
        $formatted = $message;
        
        // 移除粗体标记
        $formatted = preg_replace('/\*\*(.*?)\*\*/', '$1', $formatted);
        
        // 移除代码块标记
        $formatted = preg_replace('/`(.*?)`/', '$1', $formatted);
        
        // 保持换行和基本符号
        return $formatted;
    }

    /**
     * 为 KOOK 平台格式化消息（KMarkdown）
     * @param string $message 原始消息
     * @return string
     */
    protected function formatForKook(string $message): string
    {
        // KOOK 平台使用 KMarkdown 格式，支持更丰富的格式化
        $lines = explode("\n", $message);
        $formatted = [];

        foreach ($lines as $line) {
            $trimmed = trim($line);

            // 跳过空行
            if (empty($trimmed)) {
                $formatted[] = '';
                continue;
            }

            // 1. 处理主标题（特定 emoji 开头）
            if (preg_match('/^(📋|🤖|📊|🛠️|📝|🔌)\s+(.+)$/', $trimmed, $matches)) {
                $formatted[] = "## {$matches[1]} {$matches[2]}";
                continue;
            }

            // 2. 处理子标题（其他 emoji 开头，但不是列表项）
            if (preg_match('/^(🏓|📱|⏰|💚|📈|🔢|📅|🔧)\s+(.+)$/', $trimmed, $matches)) {
                $formatted[] = "### {$matches[1]} {$matches[2]}";
                continue;
            }

            // 3. 处理列表项（以 • 开头）
            if (preg_match('/^•\s+(.+)$/', $trimmed, $matches)) {
                // 检查是否是命令行（包含 / 和 -）
                if (preg_match('/^(\/\w+)(\s*\([^)]+\))?\s*-\s*(.+)$/', $matches[1], $cmdMatches)) {
                    $command = "`{$cmdMatches[1]}`";
                    $aliases = $cmdMatches[2] ?? '';
                    $description = $cmdMatches[3] ?? '';
                    $formatted[] = "- {$command}{$aliases} - *{$description}*";
                } else {
                    $formatted[] = "- {$matches[1]}";
                }
                continue;
            }

            // 5. 处理包含多个信息的单行（紧凑格式）
            if (preg_match_all('/(📱|⏰|💚|📊|🔧|📈|🛠️|📝|📅)\s*([^📱⏰💚📊🔧📈🛠️📝📅]+)/', $trimmed, $matches, PREG_SET_ORDER)) {
                $parts = [];
                foreach ($matches as $match) {
                    $emoji = $match[1];
                    $content = trim($match[2]);

                    // 特殊处理版本号
                    $content = preg_replace('/v(\d+\.\d+\.\d+)/', '`v$1`', $content);

                    // 特殊处理时间
                    $content = preg_replace('/(\d{4}-\d{2}-\d{2}(\s+\d{2}:\d{2}:\d{2})?)/', '`$1`', $content);

                    // 特殊处理状态
                    $content = preg_replace('/(在线|离线|运行中)/', '**$1** 🟢', $content);
                    $content = preg_replace('/(✅已初始化|❌未初始化)/', '$1', $content);

                    // 特殊处理平台
                    $content = preg_replace('/\b(qq|QQ)\b/', '**$1** 🐧', $content);
                    $content = preg_replace('/\b(kook|KOOK)\b/', '**$1** 🎮', $content);

                    // 特殊处理数字和单位
                    $content = preg_replace('/(\d+(?:\.\d+)?)\s*(MB|GB|KB|个)/', '`$1$2`', $content);

                    $parts[] = "{$emoji} {$content}";
                }

                if (!empty($parts)) {
                    $formatted[] = implode(' | ', $parts);
                    continue;
                }
            }

            // 6. 处理键值对（包含：或:）
            if (preg_match('/^([^：:]+)[：:]\s*(.+)$/', $trimmed, $matches)) {
                $key = trim($matches[1]);
                $value = trim($matches[2]);

                // 特殊处理版本号
                if (preg_match('/v\d+\.\d+\.\d+/', $value)) {
                    $value = "`{$value}`";
                }

                // 特殊处理时间
                if (preg_match('/\d{4}-\d{2}-\d{2}(\s+\d{2}:\d{2}:\d{2})?/', $value)) {
                    $value = "`{$value}`";
                }

                // 特殊处理状态
                if (preg_match('/(在线|离线|运行中)/', $value)) {
                    $value = "**{$value}** 🟢";
                } elseif (preg_match('/(已初始化)/', $value)) {
                    $value = "**{$value}** ✅";
                } elseif (preg_match('/(未初始化)/', $value)) {
                    $value = "**{$value}** ❌";
                }

                // 特殊处理平台
                if (preg_match('/(qq|QQ)/', $value)) {
                    $value = "**{$value}** 🐧";
                } elseif (preg_match('/(kook|KOOK)/', $value)) {
                    $value = "**{$value}** 🎮";
                }

                // 特殊处理数字（内存、数量等）
                if (preg_match('/(\d+(?:\.\d+)?)\s*(MB|GB|KB|个)/', $value, $numMatches)) {
                    $value = str_replace($numMatches[0], "`{$numMatches[0]}`", $value);
                }

                $formatted[] = "**{$key}**: {$value}";
                continue;
            }

            // 6. 默认保持原样
            $formatted[] = $trimmed;
        }

        // 合并结果
        $result = implode("\n", $formatted);

        // 只在主要段落之间添加分隔线（标题之间）
        $result = preg_replace('/(\n## .+\n)/', "\n---$1", $result);
        $result = preg_replace('/^---\n/', '', $result); // 移除开头的分隔线

        return $result;
    }

    /**
     * 格式化自定义结束提示语
     * @param string $message 自定义提示语
     * @param string $platform 平台名称
     * @return string 格式化后的提示语
     */
    protected function formatEndingMessageForPlatform(string $message, string $platform): string
    {
        if ($platform === 'kook') {
            return "\n\n---\n*{$message}*";
        } else {
            return "\n\n{$message}";
        }
    }

    /**
     * 获取命令结束提示信息
     * @param string $platform 平台名称
     * @return string 结束提示
     */
    protected function getEndingMessage(string $platform): string
    {
        // 尝试从配置文件获取自定义提示语
        $customMessage = $this->getCustomEndingMessage($platform);
        if ($customMessage !== null) {
            return $customMessage;
        }
        
        // 如果没有自定义配置，使用默认提示语
        $commandName = $this->getName();
        $aliases = $this->getAliases();
        
        // 构建命令列表
        $commands = [$commandName];
        if (!empty($aliases)) {
            $commands = array_merge($commands, $aliases);
        }
        
        $commandList = implode('`, `/', $commands);
        
        if ($platform === 'kook') {
            return "\n\n---\n*💡 使用 `/{$commandList}` 命令获取更多信息*";
        } else {
            return "\n\n💡 使用 /{$commandList} 命令获取更多信息";
        }
    }
    
    /**
     * 从配置文件获取自定义结束提示语
     * @param string $platform 平台名称
     * @return string|null 自定义提示语，如果没有配置则返回 null
     */
    protected function getCustomEndingMessage(string $platform): ?string
    {
        try {
            // 加载插件配置文件
            $configPath = __DIR__ . '/../config/plugins.php';
            if (!file_exists($configPath)) {
                return null;
            }
            
            $config = require $configPath;
            $endingMessages = $config['ending_messages'] ?? [];
            
            // 获取命令类名（去掉命名空间）
            $className = basename(str_replace('\\', '/', get_class($this)));
            
            // 先查找插件专属配置
            if (isset($endingMessages['plugins'][$className][$platform])) {
                $message = $endingMessages['plugins'][$className][$platform];
                return $this->formatEndingMessageForPlatform($message, $platform);
            }
            
            // 如果没有插件专属配置，使用全局默认配置
            if (isset($endingMessages['default'][$platform])) {
                $message = $endingMessages['default'][$platform];
                return $this->formatEndingMessageForPlatform($message, $platform);
            }
            
            return null;
        } catch (\Exception $e) {
            // 如果配置读取失败，返回 null 使用默认提示语
            \think\facade\Log::error("Failed to load custom ending message: " . $e->getMessage());
            return null;
        }
    }
}
