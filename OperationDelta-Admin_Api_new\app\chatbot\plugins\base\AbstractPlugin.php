<?php

namespace app\chatbot\plugins\base;

use think\facade\Log;

/**
 * 抽象插件基类
 * 提供通用的插件管理逻辑和默认实现
 */
abstract class AbstractPlugin implements PluginInterface
{
    /**
     * 插件名称
     * @var string
     */
    protected string $name = '';

    /**
     * 插件版本
     * @var string
     */
    protected string $version = '1.0.0';

    /**
     * 插件描述
     * @var string
     */
    protected string $description = '';

    /**
     * 插件作者
     * @var string
     */
    protected string $author = '';

    /**
     * 插件依赖
     * @var array
     */
    protected array $dependencies = [];

    /**
     * 插件配置
     * @var array
     */
    protected array $config = [];

    /**
     * 插件状态
     * @var string
     */
    protected string $status = 'disabled';

    /**
     * 是否已初始化
     * @var bool
     */
    protected bool $initialized = false;

    /**
     * 获取插件名称
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * 获取插件版本
     * @return string
     */
    public function getVersion(): string
    {
        return $this->version;
    }

    /**
     * 获取插件描述
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * 获取插件作者
     * @return string
     */
    public function getAuthor(): string
    {
        return $this->author;
    }

    /**
     * 获取插件依赖
     * @return array
     */
    public function getDependencies(): array
    {
        return $this->dependencies;
    }

    /**
     * 插件初始化
     * @return bool
     */
    public function initialize(): bool
    {
        if ($this->initialized) {
            return true;
        }

        try {
            // 检查依赖
            if (!$this->checkDependencies()) {
                Log::error("Plugin dependencies not met: {$this->name}");
                return false;
            }

            // 执行具体初始化逻辑
            $result = $this->onInitialize();
            if ($result) {
                $this->initialized = true;
                $this->status = 'initialized';
                Log::info("Plugin initialized: {$this->name}");
            }

            return $result;

        } catch (\Exception $e) {
            Log::error("Plugin initialization failed: {$this->name} - " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插件启用
     * @return bool
     */
    public function enable(): bool
    {
        if (!$this->initialized) {
            if (!$this->initialize()) {
                return false;
            }
        }

        try {
            $result = $this->onEnable();
            if ($result) {
                $this->status = 'enabled';
                Log::info("Plugin enabled: {$this->name}");
            }
            return $result;

        } catch (\Exception $e) {
            Log::error("Plugin enable failed: {$this->name} - " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插件禁用
     * @return bool
     */
    public function disable(): bool
    {
        try {
            $result = $this->onDisable();
            if ($result) {
                $this->status = 'disabled';
                Log::info("Plugin disabled: {$this->name}");
            }
            return $result;

        } catch (\Exception $e) {
            Log::error("Plugin disable failed: {$this->name} - " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插件卸载
     * @return bool
     */
    public function uninstall(): bool
    {
        try {
            // 先禁用插件
            if ($this->status === 'enabled') {
                $this->disable();
            }

            $result = $this->onUninstall();
            if ($result) {
                $this->status = 'uninstalled';
                $this->initialized = false;
                Log::info("Plugin uninstalled: {$this->name}");
            }
            return $result;

        } catch (\Exception $e) {
            Log::error("Plugin uninstall failed: {$this->name} - " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取插件配置
     * @return array
     */
    public function getConfig(): array
    {
        return $this->config;
    }

    /**
     * 设置插件配置
     * @param array $config
     * @return bool
     */
    public function setConfig(array $config): bool
    {
        try {
            $this->config = array_merge($this->config, $config);
            $this->onConfigChanged($config);
            return true;

        } catch (\Exception $e) {
            Log::error("Plugin config set failed: {$this->name} - " . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查插件是否可用
     * @return bool
     */
    public function isAvailable(): bool
    {
        return $this->initialized && $this->status === 'enabled';
    }

    /**
     * 获取插件状态
     * @return string
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * 检查依赖是否满足
     * @return bool
     */
    protected function checkDependencies(): bool
    {
        foreach ($this->dependencies as $dependency) {
            if (!$this->checkDependency($dependency)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查单个依赖
     * @param string $dependency
     * @return bool
     */
    protected function checkDependency(string $dependency): bool
    {
        // 这里可以添加具体的依赖检查逻辑
        // 例如：检查PHP扩展、检查其他插件等
        return true;
    }

    /**
     * 插件初始化回调（子类实现）
     * @return bool
     */
    protected function onInitialize(): bool
    {
        return true;
    }

    /**
     * 插件启用回调（子类实现）
     * @return bool
     */
    protected function onEnable(): bool
    {
        return true;
    }

    /**
     * 插件禁用回调（子类实现）
     * @return bool
     */
    protected function onDisable(): bool
    {
        return true;
    }

    /**
     * 插件卸载回调（子类实现）
     * @return bool
     */
    protected function onUninstall(): bool
    {
        return true;
    }

    /**
     * 配置变更回调（子类实现）
     * @param array $config
     */
    protected function onConfigChanged(array $config): void
    {
        // 子类可以重写此方法处理配置变更
    }

    /**
     * 获取插件信息
     * @return array
     */
    public function getInfo(): array
    {
        return [
            'name' => $this->name,
            'version' => $this->version,
            'description' => $this->description,
            'author' => $this->author,
            'dependencies' => $this->dependencies,
            'status' => $this->status,
            'initialized' => $this->initialized,
            'available' => $this->isAvailable()
        ];
    }

    /**
     * 处理命令（默认实现）
     * @param string $command 命令名称
     * @param string $args 命令参数
     * @param array $context 执行上下文
     * @return array|null 处理结果，如果不处理该命令则返回null
     */
    public function handleCommand(string $command, string $args, array $context): ?array
    {
        // 默认实现：不处理任何命令
        // 子类可以重写此方法来实现具体的命令处理逻辑
        return null;
    }
}
