<?php

namespace app\chatbot\commands\core;

use app\chatbot\commands\AbstractCommand;
use app\chatbot\commands\CommandFactory;

/**
 * 状态命令处理器
 * 显示机器人系统状态信息
 */
class StatusCommand extends AbstractCommand
{
    protected string $name = 'status';
    protected array $aliases = ['状态', 'st'];
    protected string $description = '显示机器人系统状态信息';
    protected string $usage = '/status - 查看系统状态';

    /**
     * 处理状态命令
     * @param array $args 命令参数
     * @param array $context 执行上下文
     * @return array
     */
    protected function handle(array $args, array $context): array
    {
        $platform = $context['platform'] ?? 'unknown';
        $timestamp = date('Y-m-d H:i:s');
        
        // 获取系统信息
        $systemInfo = $this->getSystemInfo();
        
        // 获取命令统计
        $commandStats = CommandFactory::getStats();
        
        $message = "🤖 机器人状态 📱 {$platform} ⏰ {$timestamp} 💚 在线\n";
        $message .= "📊 内存：{$systemInfo['memory_usage']} 峰值：{$systemInfo['peak_memory']} 运行：{$systemInfo['uptime']}\n";
        $message .= "🔧 命令：{$commandStats['total_commands']}个 别名：{$commandStats['total_aliases']}个 " . ($commandStats['initialized'] ? '✅已初始化' : '❌未初始化') . "\n";
        $message .= "📈 版本：v1.0.0";

        return $this->formatResponse(
            true,
            $message,
            'text',
            [],
            $context
        );
    }

    /**
     * 获取系统信息
     * @return array
     */
    private function getSystemInfo(): array
    {
        $memoryUsage = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);
        
        return [
            'memory_usage' => $this->formatBytes($memoryUsage),
            'peak_memory' => $this->formatBytes($peakMemory),
            'uptime' => $this->getUptime()
        ];
    }

    /**
     * 格式化字节数
     * @param int $bytes
     * @return string
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 获取运行时间
     * @return string
     */
    private function getUptime(): string
    {
        // 这里可以添加获取实际运行时间的逻辑
        // 目前返回一个示例值
        return '运行中';
    }

    /**
     * 验证命令参数
     * @param array $args 参数
     * @param array $context 上下文
     * @return array
     */
    protected function validateArgs(array $args, array $context): array
    {
        // status 命令不接受任何参数
        if (!empty($args)) {
            return [
                'valid' => false,
                'message' => '❌ status 命令不需要参数。用法：/status'
            ];
        }

        return ['valid' => true, 'message' => ''];
    }

}
