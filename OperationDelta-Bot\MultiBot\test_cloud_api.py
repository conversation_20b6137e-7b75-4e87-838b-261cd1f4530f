"""
云端API功能测试脚本
用于测试MultiBot的云端API功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.cloud_api import CloudAPIClient, CloudAPIConfig, CloudMessageProcessor
from core.models import MessageEvent, BaseMessage, BaseUser, BaseGroup, PlatformType
from config import ConfigManager


async def test_cloud_api():
    """测试云端API功能"""
    print("=== 云端API功能测试 ===")
    
    # 1. 加载配置
    print("\n1. 加载配置...")
    config_manager = ConfigManager("config.yml")
    global_config = config_manager.get_global_config()
    
    print(f"云端API启用状态: {global_config.get('cloud_api_enabled')}")
    print(f"云端API地址: {global_config.get('cloud_api_base_url')}")
    print(f"云端API超时: {global_config.get('cloud_api_timeout')}秒")
    
    if not global_config.get('cloud_api_enabled'):
        print("❌ 云端API未启用，请检查配置文件")
        return
    
    # 2. 创建云端API客户端
    print("\n2. 创建云端API客户端...")
    cloud_config = CloudAPIConfig(
        enabled=global_config.get('cloud_api_enabled', False),
        base_url=global_config.get('cloud_api_base_url', ''),
        auth_token=global_config.get('cloud_api_auth_token', ''),
        timeout=global_config.get('cloud_api_timeout', 30),
        max_retries=global_config.get('cloud_api_max_retries', 3)
    )
    
    api_client = CloudAPIClient(cloud_config)
    
    # 3. 测试健康检查
    print("\n3. 测试健康检查...")
    try:
        health = await api_client.health_check()
        if health:
            print("✅ 健康检查成功")
        else:
            print("❌ 健康检查失败")
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
    
    # 4. 测试获取命令列表
    print("\n4. 测试获取命令列表...")
    try:
        commands = await api_client.get_commands()
        if commands:
            print(f"✅ 获取到 {len(commands)} 个命令:")
            for cmd in commands:
                print(f"  - {cmd.get('name')}: {cmd.get('description')}")
        else:
            print("❌ 未获取到命令列表")
    except Exception as e:
        print(f"❌ 获取命令列表异常: {e}")
    
    # 5. 测试发送消息
    print("\n5. 测试发送消息...")
    try:
        # 创建测试消息
        user = BaseUser(user_id="test_user", username="测试用户", nickname="测试用户")
        group = BaseGroup(group_id="test_group", group_name="测试群组")
        message = BaseMessage(
            message_id="test_msg_001",
            content="/help",  # 改为命令消息
            sender=user,
            group=group,
            timestamp=datetime.now()
        )

        reply = await api_client.send_message(message)
        if reply:
            print(f"✅ 消息发送成功，回复: {reply}")
        else:
            print("❌ 消息发送失败或无回复")
    except Exception as e:
        print(f"❌ 发送消息异常: {e}")
    
    # 6. 测试执行命令
    print("\n6. 测试执行命令...")
    test_commands = [
        ("echo", "你好世界"),
        ("time", ""),
        ("weather", "北京"),
        ("calc", "2+3*4")
    ]
    
    for cmd_name, params in test_commands:
        try:
            result = await api_client.execute_command(cmd_name, {
                "params": params,
                "platform": PlatformType.KOOK.value,
                "sender_id": "test_user",
                "group_id": "test_group"
            })
            
            if result and result.get("success"):
                print(f"✅ 命令 '{cmd_name}' 执行成功: {result.get('response')}")
            else:
                print(f"❌ 命令 '{cmd_name}' 执行失败")
        except Exception as e:
            print(f"❌ 命令 '{cmd_name}' 执行异常: {e}")
    
    # 7. 测试消息处理器
    print("\n7. 测试消息处理器...")
    try:
        message_processor = CloudMessageProcessor(api_client)
        
        # 创建测试事件
        event = MessageEvent(
            event_id="test_event_001",
            event_type="message",
            message=message,
            timestamp=datetime.now(),
            platform=PlatformType.KOOK
        )
        
        # 测试普通消息处理
        result = await message_processor.process_message(event)
        if result is None:
            print("✅ 普通消息处理正常（无回复）")
        else:
            print(f"✅ 普通消息处理返回: {result}")
        
        # 测试命令消息处理
        command_message = BaseMessage(
            message_id="test_cmd_001",
            content="/echo 测试命令",
            sender=user,
            group=group,
            timestamp=datetime.now()
        )
        command_event = MessageEvent(
            event_id="test_cmd_event_001",
            event_type="message",
            message=command_message,
            timestamp=datetime.now(),
            platform=PlatformType.KOOK
        )
        
        result = await message_processor.process_message(command_event)
        if result:
            print(f"✅ 命令消息处理成功: {result}")
        else:
            print("❌ 命令消息处理失败")
            
    except Exception as e:
        print(f"❌ 消息处理器测试异常: {e}")
    
    # 8. 关闭客户端
    print("\n8. 关闭客户端...")
    await api_client.close()
    print("✅ 客户端已关闭")
    
    print("\n=== 测试完成 ===")


async def main():
    """主函数"""
    try:
        await test_cloud_api()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
