<?php

namespace app\chatbot\plugins\base;

/**
 * 插件接口
 * 定义所有插件必须实现的方法
 */
interface PluginInterface
{
    /**
     * 获取插件名称
     * @return string 插件名称
     */
    public function getName(): string;

    /**
     * 获取插件版本
     * @return string 插件版本
     */
    public function getVersion(): string;

    /**
     * 获取插件描述
     * @return string 插件描述
     */
    public function getDescription(): string;

    /**
     * 获取插件作者
     * @return string 插件作者
     */
    public function getAuthor(): string;

    /**
     * 获取插件依赖
     * @return array 依赖列表
     */
    public function getDependencies(): array;

    /**
     * 插件初始化
     * @return bool 是否初始化成功
     */
    public function initialize(): bool;

    /**
     * 插件启用
     * @return bool 是否启用成功
     */
    public function enable(): bool;

    /**
     * 插件禁用
     * @return bool 是否禁用成功
     */
    public function disable(): bool;

    /**
     * 插件卸载
     * @return bool 是否卸载成功
     */
    public function uninstall(): bool;

    /**
     * 获取插件配置
     * @return array 配置信息
     */
    public function getConfig(): array;

    /**
     * 设置插件配置
     * @param array $config 配置信息
     * @return bool 是否设置成功
     */
    public function setConfig(array $config): bool;

    /**
     * 检查插件是否可用
     * @return bool 是否可用
     */
    public function isAvailable(): bool;

    /**
     * 获取插件状态
     * @return string 插件状态
     */
    public function getStatus(): string;

    /**
     * 处理命令
     * @param string $command 命令名称
     * @param string $args 命令参数
     * @param array $context 执行上下文
     * @return array|null 处理结果，如果不处理该命令则返回null
     */
    public function handleCommand(string $command, string $args, array $context): ?array;
}
