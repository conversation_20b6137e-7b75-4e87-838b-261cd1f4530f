# Chatbot API 文档

## 概述

本文档详细描述了 Chatbot 模块的所有 API 接口，包括请求参数、响应格式、错误码和使用示例。

## 基础信息

- **基础URL**: `/chatbot`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`
- **认证方式**: 基于 Token 的认证（部分接口）

## 通用响应格式

所有 API 响应都遵循统一的格式：

```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据
    },
    "timestamp": **********
}
```

### 响应字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| `code` | integer | 状态码，200表示成功 |
| `message` | string | 响应消息 |
| `data` | object/array | 响应数据 |
| `timestamp` | integer | 响应时间戳 |

### 错误响应格式

```json
{
    "code": 400,
    "message": "错误描述",
    "data": null,
    "timestamp": **********
}
```

## 状态码说明

| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## API 接口列表

### 1. 健康检查

**接口地址**: `GET /chatbot/health`

**功能描述**: 检查 API 服务状态

**请求参数**: 无

**响应示例**:
```json
{
    "code": 200,
    "message": "服务正常",
    "data": {
        "status": "ok",
        "timestamp": **********,
        "version": "1.0.0",
        "service": "chatbot-api",
        "uptime": 3600,
        "memory_usage": "128MB",
        "cpu_usage": "5%"
    },
    "timestamp": **********
}
```

**响应字段说明**:
| 字段 | 类型 | 描述 |
|------|------|------|
| `status` | string | 服务状态 |
| `timestamp` | integer | 检查时间戳 |
| `version` | string | API版本 |
| `service` | string | 服务名称 |
| `uptime` | integer | 运行时间（秒） |
| `memory_usage` | string | 内存使用情况 |
| `cpu_usage` | string | CPU使用情况 |

### 2. 命令处理

**接口地址**: `POST /chatbot/command/process`

**功能描述**: 处理机器人命令

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {token}  // 可选
```

**请求参数**:
```json
{
    "message": "/help",
    "platform": "kook",
    "user_id": "123456",
    "username": "用户名",
    "nickname": "昵称",
    "display_name": "显示名称",
    "avatar": "https://example.com/avatar.jpg",
    "group_id": "789012",
    "group_name": "群组名称",
    "member_count": 100,
    "channel_id": "channel123",
    "channel_name": "频道名称",
    "message_id": "msg123",
    "timestamp": **********
}
```

**请求字段说明**:
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| `message` | string | 是 | 用户发送的消息内容 |
| `platform` | string | 是 | 平台标识（kook, qq, discord等） |
| `user_id` | string | 是 | 用户ID |
| `username` | string | 否 | 用户名 |
| `nickname` | string | 否 | 昵称 |
| `display_name` | string | 否 | 显示名称 |
| `avatar` | string | 否 | 头像URL |
| `group_id` | string | 否 | 群组ID |
| `group_name` | string | 否 | 群组名称 |
| `member_count` | integer | 否 | 群组成员数量 |
| `channel_id` | string | 否 | 频道ID |
| `channel_name` | string | 否 | 频道名称 |
| `message_id` | string | 否 | 消息ID |
| `timestamp` | integer | 否 | 消息时间戳 |

**成功响应示例**:
```json
{
    "code": 200,
    "message": "命令执行成功",
    "data": {
        "success": true,
        "message": "📋 可用命令列表：\n\n• /help (帮助, h, ?)\n  显示帮助信息，列出所有可用命令\n\n• /ping (p)\n  测试连接状态\n\n💡 使用 /help [命令名] 查看具体命令的详细帮助",
        "type": "text",
        "timestamp": **********,
        "execution_time": 0.023,
        "command": "help",
        "args": []
    },
    "timestamp": **********
}
```

**错误响应示例**:
```json
{
    "code": 400,
    "message": "命令未找到",
    "data": {
        "success": false,
        "error": "未找到命令：unknown_command",
        "suggestions": ["help", "ping", "status"],
        "timestamp": **********
    },
    "timestamp": **********
}
```

**响应字段说明**:
| 字段 | 类型 | 描述 |
|------|------|------|
| `success` | boolean | 命令是否执行成功 |
| `message` | string | 响应消息内容 |
| `type` | string | 消息类型（text, image, file等） |
| `timestamp` | integer | 执行时间戳 |
| `execution_time` | float | 执行耗时（秒） |
| `command` | string | 执行的命令名称 |
| `args` | array | 命令参数 |
| `error` | string | 错误信息（失败时） |
| `suggestions` | array | 建议的命令（命令未找到时） |

### 3. 用户管理

#### 3.1 获取用户信息

**接口地址**: `GET /chatbot/user/{user_id}`

**功能描述**: 获取指定用户的信息

**路径参数**:
| 参数 | 类型 | 描述 |
|------|------|------|
| `user_id` | string | 用户ID |

**查询参数**:
| 参数 | 类型 | 描述 |
|------|------|------|
| `platform` | string | 平台标识（可选） |

**响应示例**:
```json
{
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
        "user_id": "123456",
        "platform": "kook",
        "username": "用户名",
        "nickname": "昵称",
        "display_name": "显示名称",
        "avatar": "https://example.com/avatar.jpg",
        "created_at": "2024-01-01 12:00:00",
        "last_active": "2024-01-01 13:30:00",
        "message_count": 150,
        "command_count": 25,
        "status": "active"
    },
    "timestamp": **********
}
```

#### 3.2 更新用户信息

**接口地址**: `PUT /chatbot/user/{user_id}`

**功能描述**: 更新指定用户的信息

**请求参数**:
```json
{
    "username": "新用户名",
    "nickname": "新昵称",
    "display_name": "新显示名称",
    "avatar": "https://example.com/new-avatar.jpg"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "用户信息更新成功",
    "data": {
        "user_id": "123456",
        "updated_fields": ["username", "nickname"],
        "timestamp": **********
    },
    "timestamp": **********
}
```

### 4. 群组管理

#### 4.1 获取群组信息

**接口地址**: `GET /chatbot/group/{group_id}`

**功能描述**: 获取指定群组的信息

**响应示例**:
```json
{
    "code": 200,
    "message": "获取群组信息成功",
    "data": {
        "group_id": "789012",
        "platform": "kook",
        "group_name": "群组名称",
        "member_count": 100,
        "created_at": "2024-01-01 12:00:00",
        "last_active": "2024-01-01 13:30:00",
        "message_count": 500,
        "command_count": 80,
        "status": "active"
    },
    "timestamp": **********
}
```

#### 4.2 更新群组信息

**接口地址**: `PUT /chatbot/group/{group_id}`

**功能描述**: 更新指定群组的信息

**请求参数**:
```json
{
    "group_name": "新群组名称",
    "member_count": 120
}
```

### 5. 插件管理

#### 5.1 获取插件列表

**接口地址**: `GET /chatbot/plugins`

**功能描述**: 获取所有插件的列表

**查询参数**:
| 参数 | 类型 | 描述 |
|------|------|------|
| `status` | string | 插件状态过滤（enabled, disabled, all） |
| `type` | string | 插件类型过滤（core, custom, third_party） |

**响应示例**:
```json
{
    "code": 200,
    "message": "获取插件列表成功",
    "data": {
        "plugins": [
            {
                "name": "weather",
                "version": "1.0.0",
                "description": "天气查询插件",
                "author": "OperationDelta Team",
                "status": "enabled",
                "type": "custom",
                "dependencies": ["ext:curl"],
                "commands": ["weather", "cities"],
                "created_at": "2024-01-01 12:00:00",
                "updated_at": "2024-01-01 13:30:00"
            }
        ],
        "total": 1,
        "enabled_count": 1,
        "disabled_count": 0
    },
    "timestamp": **********
}
```

#### 5.2 启用插件

**接口地址**: `POST /chatbot/plugins/{plugin_name}/enable`

**功能描述**: 启用指定插件

**响应示例**:
```json
{
    "code": 200,
    "message": "插件启用成功",
    "data": {
        "plugin_name": "weather",
        "status": "enabled",
        "timestamp": **********
    },
    "timestamp": **********
}
```

#### 5.3 禁用插件

**接口地址**: `POST /chatbot/plugins/{plugin_name}/disable`

**功能描述**: 禁用指定插件

#### 5.4 获取插件详情

**接口地址**: `GET /chatbot/plugins/{plugin_name}`

**功能描述**: 获取指定插件的详细信息

**响应示例**:
```json
{
    "code": 200,
    "message": "获取插件详情成功",
    "data": {
        "name": "weather",
        "version": "1.0.0",
        "description": "天气查询插件，提供实时天气信息查询功能",
        "author": "OperationDelta Team",
        "status": "enabled",
        "type": "custom",
        "dependencies": ["ext:curl"],
        "commands": ["weather", "cities"],
        "config": {
            "api_key": "demo_key",
            "default_city": "北京"
        },
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 13:30:00",
        "last_error": null,
        "usage_stats": {
            "total_commands": 150,
            "successful_commands": 145,
            "failed_commands": 5
        }
    },
    "timestamp": **********
}
```

### 6. 命令管理

#### 6.1 获取命令列表

**接口地址**: `GET /chatbot/commands`

**功能描述**: 获取所有可用命令的列表

**查询参数**:
| 参数 | 类型 | 描述 |
|------|------|------|
| `category` | string | 命令分类过滤 |
| `permission` | string | 权限级别过滤 |

**响应示例**:
```json
{
    "code": 200,
    "message": "获取命令列表成功",
    "data": {
        "commands": [
            {
                "name": "help",
                "aliases": ["帮助", "h", "?"],
                "description": "显示帮助信息，列出所有可用命令",
                "usage": "/help [命令名] - 显示指定命令的详细帮助",
                "category": "core",
                "permission": "all",
                "plugin": null
            },
            {
                "name": "weather",
                "aliases": ["天气"],
                "description": "查询指定城市的天气信息",
                "usage": "/weather [城市名] - 查询天气信息",
                "category": "utility",
                "permission": "all",
                "plugin": "weather"
            }
        ],
        "total": 2,
        "categories": ["core", "utility", "game"],
        "permissions": ["all", "admin", "moderator"]
    },
    "timestamp": **********
}
```

#### 6.2 获取命令详情

**接口地址**: `GET /chatbot/commands/{command_name}`

**功能描述**: 获取指定命令的详细信息

**响应示例**:
```json
{
    "code": 200,
    "message": "获取命令详情成功",
    "data": {
        "name": "help",
        "aliases": ["帮助", "h", "?"],
        "description": "显示帮助信息，列出所有可用命令",
        "usage": "/help [命令名] - 显示指定命令的详细帮助",
        "category": "core",
        "permission": "all",
        "plugin": null,
        "help": "帮助命令用于显示所有可用命令的列表和详细信息。\n\n使用方法：\n/help - 显示所有命令列表\n/help [命令名] - 显示指定命令的详细帮助",
        "examples": [
            "/help",
            "/help ping",
            "/help weather"
        ],
        "created_at": "2024-01-01 12:00:00",
        "usage_stats": {
            "total_usage": 500,
            "successful_usage": 495,
            "failed_usage": 5,
            "last_used": "2024-01-01 13:30:00"
        }
    },
    "timestamp": **********
}
```

### 7. 统计信息

#### 7.1 获取系统统计

**接口地址**: `GET /chatbot/stats`

**功能描述**: 获取系统整体统计信息

**响应示例**:
```json
{
    "code": 200,
    "message": "获取统计信息成功",
    "data": {
        "system": {
            "uptime": 3600,
            "memory_usage": "128MB",
            "cpu_usage": "5%",
            "disk_usage": "2.5GB",
            "active_connections": 10
        },
        "users": {
            "total": 1000,
            "active_today": 150,
            "active_week": 300,
            "new_today": 25
        },
        "groups": {
            "total": 50,
            "active_today": 30,
            "active_week": 45
        },
        "commands": {
            "total_executed": 5000,
            "successful": 4850,
            "failed": 150,
            "popular_commands": [
                {"name": "help", "count": 800},
                {"name": "ping", "count": 600},
                {"name": "weather", "count": 400}
            ]
        },
        "plugins": {
            "total": 5,
            "enabled": 4,
            "disabled": 1,
            "popular_plugins": [
                {"name": "weather", "usage": 400},
                {"name": "music", "usage": 200},
                {"name": "game", "usage": 150}
            ]
        },
        "performance": {
            "avg_response_time": 0.023,
            "max_response_time": 0.150,
            "requests_per_minute": 60
        }
    },
    "timestamp": **********
}
```

## 错误码详细说明

### 400 系列错误

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 401 | 未授权访问 | 提供有效的认证信息 |
| 403 | 权限不足 | 检查用户权限设置 |
| 404 | 资源不存在 | 确认资源ID是否正确 |

### 500 系列错误

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 500 | 服务器内部错误 | 查看服务器日志 |
| 502 | 网关错误 | 检查上游服务状态 |
| 503 | 服务不可用 | 等待服务恢复 |

## 使用示例

### cURL 示例

#### 健康检查
```bash
curl -X GET "http://localhost/chatbot/health" \
  -H "Content-Type: application/json"
```

#### 命令处理
```bash
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/help",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'
```

#### 启用插件
```bash
curl -X POST "http://localhost/chatbot/plugins/weather/enable" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token"
```

### JavaScript 示例

#### 健康检查
```javascript
fetch('/chatbot/health')
  .then(response => response.json())
  .then(data => console.log(data));
```

#### 命令处理
```javascript
fetch('/chatbot/command/process', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: '/help',
    platform: 'kook',
    user_id: '123456',
    username: '测试用户'
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

### Python 示例

#### 健康检查
```python
import requests

response = requests.get('http://localhost/chatbot/health')
data = response.json()
print(data)
```

#### 命令处理
```python
import requests

data = {
    'message': '/help',
    'platform': 'kook',
    'user_id': '123456',
    'username': '测试用户'
}

response = requests.post('http://localhost/chatbot/command/process', json=data)
result = response.json()
print(result)
```

## 注意事项

1. **请求频率限制**: API 有请求频率限制，建议合理控制请求频率
2. **数据格式**: 所有请求和响应都使用 JSON 格式
3. **字符编码**: 确保使用 UTF-8 编码处理中文内容
4. **错误处理**: 始终检查响应状态码和错误信息
5. **版本兼容**: 不同版本的 API 可能有差异，请查看版本说明

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础命令处理接口
- 实现插件管理接口
- 实现用户和群组管理接口
- 实现统计信息接口
