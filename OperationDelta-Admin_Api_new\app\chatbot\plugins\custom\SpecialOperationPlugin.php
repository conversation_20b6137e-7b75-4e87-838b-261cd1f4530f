<?php

namespace app\chatbot\plugins\custom;

use app\chatbot\plugins\base\AbstractPlugin;
use app\api\service\SpecialOperationService;
use think\facade\Log;

/**
 * 特勤处插件
 * 提供特勤处数据查询功能
 */
class SpecialOperationPlugin extends AbstractPlugin
{
    /**
     * 插件名称
     */
    protected string $name = 'SpecialOperationPlugin';

    /**
     * 插件版本
     */
    protected string $version = '1.0.0';

    /**
     * 插件描述
     */
    protected string $description = '特勤处数据查询插件，提供各位置最高利润特勤处信息';

    /**
     * 插件作者
     */
    protected string $author = 'OperationDelta';

    /**
     * 插件依赖
     */
    protected array $dependencies = [];

    /**
     * 支持的命令关键词
     */
    private array $supportedCommands = [
        '特勤处',
        '特勤',
        '利润',
        '最高利润'
    ];

    /**
     * 特勤处服务实例
     */
    private ?SpecialOperationService $specialOperationService = null;

    /**
     * 插件配置默认值
     */
    protected array $config = [
        'show_details' => true,
        'max_display_items' => 4
    ];

    /**
     * 插件初始化回调
     * @return bool
     */
    protected function onInitialize(): bool
    {
        try {
            // 检查依赖
            if (!$this->checkDependencies()) {
                Log::error("SpecialOperationPlugin dependencies check failed");
                return false;
            }

            // 初始化特勤处服务
            $this->specialOperationService = new SpecialOperationService();
            
            // 验证配置
            $this->validateConfig();
            
            Log::info("SpecialOperationPlugin initialized successfully with config: " . json_encode($this->config));
            return true;
        } catch (\Exception $e) {
            Log::error("SpecialOperationPlugin initialization failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插件启用回调
     * @return bool
     */
    protected function onEnable(): bool
    {
        try {
            Log::info("SpecialOperationPlugin enabled - supporting commands: " . implode(', ', $this->supportedCommands));
            return true;
        } catch (\Exception $e) {
            Log::error("SpecialOperationPlugin enable failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 插件禁用回调
     * @return bool
     */
    protected function onDisable(): bool
    {
        try {
            Log::info("SpecialOperationPlugin disabled");
            return true;
        } catch (\Exception $e) {
            Log::error("SpecialOperationPlugin disable failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 配置变更回调
     * @param array $config
     */
    protected function onConfigChanged(array $config): void
    {
        Log::info("SpecialOperationPlugin config changed: " . json_encode($config));
        $this->validateConfig();
    }

    /**
     * 初始化特勤处服务
     * @return bool
     */
    private function initializeSpecialOperationService(): bool
    {
        try {
            if ($this->specialOperationService === null) {
                $this->specialOperationService = new SpecialOperationService();
                Log::info("SpecialOperationService initialized successfully");
            }
            return true;
        } catch (\Exception $e) {
            Log::error("SpecialOperationService initialization failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理命令
     * @param string $command 命令名称
     * @param string $args 命令参数
     * @param array $context 执行上下文
     * @return array|null 处理结果
     */
    public function handleCommand(string $command, string $args, array $context): ?array
    {
        // 检查是否是支持的特勤处命令
        if (!in_array($command, $this->supportedCommands)) {
            return null;
        }

        // 解析命令参数
        $params = $this->parseCommandArgs($args);

        // 记录参数解析结果（用于调试）
        Log::info("SpecialOperationPlugin: Parsed params from args '{$args}': " . json_encode($params));

        // 验证参数
        $validation = $this->validateArgs($params, $context);
        if (!$validation['valid']) {
            return $this->formatErrorResponse($validation['message'], 'Invalid arguments', $context);
        }

        try {
            // 初始化特勤处服务
            if (!$this->initializeSpecialOperationService()) {
                return $this->formatErrorResponse('特勤处服务初始化失败，请稍后重试', 'SpecialOperationService not available', $context);
            }

            // 检查数据库表状态
            $dbStatus = $this->checkDatabaseStatus();
            Log::info("SpecialOperationPlugin: Database status check: " . json_encode($dbStatus));

            // 如果表不存在或没有数据，直接返回错误信息
            if (!$dbStatus['table_exists']) {
                return $this->formatErrorResponse('数据库表不存在，请联系管理员', 'Table does not exist', $context);
            }

            if ($dbStatus['active_records'] == 0) {
                return $this->formatErrorResponse('数据库中暂无有效的特勤处数据', 'No active records found', $context);
            }

            // 根据参数决定查询方式
            if (empty($params) || (!isset($params['location']) && !isset($params['min_profit']) && !isset($params['max_cycle']))) {
                // 无参数或无有效筛选条件，使用默认的各位置最高利润查询
                Log::info("SpecialOperationPlugin: Using fetchTopProfitByLocation query");
                $specialOperationData = $this->specialOperationService->fetchTopProfitByLocation();
                $queryType = 'top_by_location';
            } else {
                // 有参数，使用带参数的查询
                Log::info("SpecialOperationPlugin: Using filtered query with params: " . json_encode($params));
                $specialOperationData = $this->specialOperationService->getSpecialOperationListWithCache($params);
                $queryType = 'filtered_list';
            }

            // 记录查询结果
            Log::info("SpecialOperationPlugin: Query result - type: {$queryType}, data: " . json_encode($specialOperationData));

            // 验证数据格式
            if (!is_array($specialOperationData)) {
                Log::error("SpecialOperationPlugin: Invalid data type returned from service. Type: " . gettype($specialOperationData));
                return $this->formatErrorResponse('特勤处数据格式错误，请稍后重试', 'Invalid data type: ' . gettype($specialOperationData), $context);
            }

            // 记录成功信息
            Log::info("SpecialOperationPlugin: Successfully retrieved special operation data for command: {$command}, query_type: {$queryType}, params: " . json_encode($params));

            // 格式化并返回响应
            return $this->formatSpecialOperationResponse($specialOperationData, $context, $queryType, $params);

        } catch (\Exception $e) {
            Log::error("SpecialOperationPlugin command failed: " . $e->getMessage());
            return $this->formatErrorResponse('获取特勤处信息失败，请稍后重试', $e->getMessage(), $context);
        }
    }

    /**
     * 解析命令参数
     * @param string $args 命令参数字符串
     * @return array 解析后的参数数组
     */
    private function parseCommandArgs(string $args): array
    {
        $params = [];
        $args = trim($args);

        if (empty($args)) {
            return $params;
        }

        // 支持的参数格式：
        // /特勤处 工作台
        // /特勤处 工作台 利润>=1000
        // /特勤处 利润>=1000 周期<=2
        // /特勤处 工作台 利润>=1000 周期<=2

        // 分割参数
        $argParts = preg_split('/\s+/', $args);

        foreach ($argParts as $part) {
            $part = trim($part);
            if (empty($part)) continue;

            // 检查是否是位置参数
            if (in_array($part, ['工作台', '技术中心', '制药台', '防具'])) {
                $params['location'] = $part;
                continue;
            }

            // 检查是否是利润条件
            if (preg_match('/^利润([><=]+)(\d+)$/', $part, $matches)) {
                $operator = $matches[1];
                $value = (int)$matches[2];

                if (in_array($operator, ['>=', '>'])) {
                    $params['min_profit'] = $value;
                } elseif (in_array($operator, ['<=', '<'])) {
                    $params['max_profit'] = $value;
                }
                continue;
            }

            // 检查是否是周期条件
            if (preg_match('/^周期([><=]+)(\d+)$/', $part, $matches)) {
                $operator = $matches[1];
                $value = (int)$matches[2];

                if (in_array($operator, ['<=', '<'])) {
                    $params['max_cycle'] = $value;
                } elseif (in_array($operator, ['>=', '>'])) {
                    $params['min_cycle'] = $value;
                }
                continue;
            }

            // 检查是否是时薪条件
            if (preg_match('/^时薪([><=]+)(\d+)$/', $part, $matches)) {
                $operator = $matches[1];
                $value = (int)$matches[2];

                if (in_array($operator, ['>=', '>'])) {
                    $params['min_hourly_profit'] = $value;
                }
                continue;
            }
        }

        return $params;
    }

    /**
     * 验证命令参数
     * @param array $params 解析后的参数
     * @param array $context 执行上下文
     * @return array
     */
    private function validateArgs(array $params, array $context): array
    {
        // 验证位置参数
        if (isset($params['location']) && !in_array($params['location'], ['工作台', '技术中心', '制药台', '防具'])) {
            return ['valid' => false, 'message' => '无效的位置参数，支持：工作台、技术中心、制药台、防具'];
        }

        // 验证利润参数
        if (isset($params['min_profit']) && $params['min_profit'] < 0) {
            return ['valid' => false, 'message' => '最小利润不能为负数'];
        }

        // 验证周期参数
        if (isset($params['max_cycle']) && $params['max_cycle'] <= 0) {
            return ['valid' => false, 'message' => '最大周期必须大于0'];
        }

        return ['valid' => true, 'message' => ''];
    }

    /**
     * 格式化错误响应
     * @param string $message 用户友好的错误消息
     * @param string $error 技术错误信息
     * @param array $context 执行上下文（用于平台检测）
     * @return array
     */
    private function formatErrorResponse(string $message, string $error, array $context = []): array
    {
        // 根据平台格式化错误消息
        $platform = $context['platform'] ?? 'qq';
        $formattedMessage = $this->formatMessageForPlatform($message, $platform);
        
        // 添加全局结束提示
        $endingMessage = $this->getEndingMessage($platform);
        $formattedMessage .= $endingMessage;
        
        return [
            'handled' => true,
            'success' => false,
            'message' => $formattedMessage,
            'type' => $platform === 'kook' ? 'kmarkdown' : 'text',
            'platform' => $platform,
            'error' => $error,
            'data' => null
        ];
    }

    /**
     * 验证插件配置
     */
    private function validateConfig(): void
    {
        if (!isset($this->config['show_details'])) {
            $this->config['show_details'] = true;
        }
        if (!isset($this->config['max_display_items']) || $this->config['max_display_items'] < 1) {
            $this->config['max_display_items'] = 4;
        }
    }

    /**
     * 格式化特勤处响应
     * @param array $specialOperationData 特勤处数据
     * @param array $context 执行上下文（用于平台检测）
     * @param string $queryType 查询类型
     * @param array $params 查询参数
     * @return array 格式化后的响应
     */
    private function formatSpecialOperationResponse(array $specialOperationData, array $context = [], string $queryType = 'top_by_location', array $params = []): array
    {
        // 安全地获取和转换数据
        $list = $this->safeArray($specialOperationData['list'] ?? []);
        $total = $this->safeInt($specialOperationData['total'] ?? 0);

        // 根据查询类型构建标题和描述
        if ($queryType === 'top_by_location') {
            $specialOperationText = "💼 特勤处最高利润推荐\n";
            $specialOperationText .= "📊 共查询 {$total} 个位置\n";
            $specialOperationText .= "⚠️ 技术中心已过滤周期≥16h项目\n\n";
        } else {
            $specialOperationText = "💼 特勤处查询结果\n";
            $specialOperationText .= "📊 共找到 {$total} 个项目";

            // 显示查询条件
            $conditions = [];
            if (isset($params['location'])) {
                $conditions[] = "位置：{$params['location']}";
            }
            if (isset($params['min_profit'])) {
                $conditions[] = "利润≥{$params['min_profit']}";
            }
            if (isset($params['max_cycle'])) {
                $conditions[] = "周期≤{$params['max_cycle']}h";
            }
            if (isset($params['min_hourly_profit'])) {
                $conditions[] = "时薪≥{$params['min_hourly_profit']}";
            }

            if (!empty($conditions)) {
                $specialOperationText .= " | 条件：" . implode('，', $conditions);
            }

            // 如果查询技术中心或全部位置，添加过滤提示
            if (!isset($params['location']) || $params['location'] === '技术中心') {
                $specialOperationText .= "\n⚠️ 技术中心已过滤周期≥16h项目";
            }
            $specialOperationText .= "\n\n";
        }

        if (!empty($list)) {
            if ($queryType === 'top_by_location') {
                $specialOperationText .= "📋 各位置最高利润项目\n";
                $maxDisplay = min($this->config['max_display_items'], 4);
            } else {
                $specialOperationText .= "📋 查询结果列表\n";
                $maxDisplay = min($this->config['max_display_items'], 10); // 参数查询可以显示更多
            }
            $displayCount = 0;

            foreach ($list as $index => $item) {
                if ($displayCount >= $maxDisplay) {
                    break;
                }

                try {
                    $name = $this->safeString($item['name'] ?? "未知项目");
                    $place = $this->safeString($item['place'] ?? "未知位置");
                    $lirun = $this->safeFloat($item['lirun'] ?? 0);
                    $period = $this->safeInt($item['period'] ?? 0);
                    $profitPerHour = $this->safeFloat($item['profit_per_hour'] ?? 0);
                    $grade = $this->safeInt($item['grade'] ?? 0);

                    // 获取等级颜色和名称显示
                    $gradeDisplay = $this->getGradeDisplay($grade, $name);

                    // 美观显示
                    $specialOperationText .= "• {$place}：{$gradeDisplay}\n";
                    $specialOperationText .= "  💰 利润：{$lirun} | ⏱️ 周期：{$period}h | 📈 时薪：{$profitPerHour}\n";
                    $displayCount++;
                } catch (\Exception $e) {
                    Log::error("SpecialOperationPlugin: Error processing item {$index}: " . $e->getMessage());
                    continue;
                }
            }
        } else {
            $specialOperationText .= "⚠️ 暂无特勤处数据\n";

            // 如果是开发环境或调试模式，显示更多诊断信息
            if (config('app.debug', false)) {
                $dbStatus = $this->checkDatabaseStatus();
                if (!$dbStatus['table_exists']) {
                    $specialOperationText .= "🔍 诊断：数据库表不存在\n";
                } elseif ($dbStatus['active_records'] == 0) {
                    $specialOperationText .= "🔍 诊断：数据库中无有效记录\n";
                    $specialOperationText .= "📊 总记录数：{$dbStatus['total_records']}\n";
                    $specialOperationText .= "📊 有效记录数：{$dbStatus['active_records']}\n";
                } else {
                    $specialOperationText .= "🔍 诊断：查询条件可能过于严格\n";
                }
            }
        }

        // 根据平台格式化消息并添加全局结束提示
        $platform = $context['platform'] ?? 'qq';
        $formattedMessage = $this->formatMessageForPlatform($specialOperationText, $platform);

        // 添加全局结束提示
        $endingMessage = $this->getEndingMessage($platform);
        $formattedMessage .= $endingMessage;

        return [
            'handled' => true,
            'success' => true,
            'message' => $formattedMessage,
            'type' => $platform === 'kook' ? 'kmarkdown' : 'text',
            'platform' => $platform,
            'data' => [
                'total' => $total,
                'list' => $list
            ]
        ];
    }

    /**
     * 获取插件支持的命令列表
     * @return array 支持的命令列表
     */
    public function getSupportedCommands(): array
    {
        return $this->supportedCommands;
    }

    /**
     * 安全地转换为字符串
     */
    private function safeString($value): string
    {
        return is_string($value) ? $value : (string)$value;
    }

    /**
     * 安全地转换为整数
     */
    private function safeInt($value): int
    {
        return is_numeric($value) ? (int)$value : 0;
    }

    /**
     * 安全地转换为浮点数
     */
    private function safeFloat($value): float
    {
        return is_numeric($value) ? (float)$value : 0.0;
    }

    /**
     * 安全地转换为数组
     */
    private function safeArray($value): array
    {
        return is_array($value) ? $value : [];
    }

    /**
     * 获取等级显示（带颜色）
     * @param int $grade 物品等级
     * @param string $name 物品名称
     * @return string 带颜色的物品名称
     */
    private function getGradeDisplay(int $grade, string $name): string
    {
        // 根据前端等级颜色配置返回对应的颜色标识
        // --grade-bg-0: #949595 (灰色)
        // --grade-bg-1: #cdd5d5 (浅灰)
        // --grade-bg-2: #188D14 (绿色)
        // --grade-bg-3: #658BCE (蓝色)
        // --grade-bg-4: #9b61c8 (紫色)
        // --grade-bg-5: #e8a64e (橙色)
        // --grade-bg-6: #cb464a (红色)
        switch ($grade) {
            case 0:
                return "⚫ {$name}"; // 黑色圆点 - 对应灰色背景 #949595
            case 1:
                return "⚪ {$name}"; // 白色圆点 - 对应浅灰背景 #cdd5d5
            case 2:
                return "🟢 {$name}"; // 绿色圆点 - 对应绿色背景 #188D14
            case 3:
                return "🔵 {$name}"; // 蓝色圆点 - 对应蓝色背景 #658BCE
            case 4:
                return "🟣 {$name}"; // 紫色圆点 - 对应紫色背景 #9b61c8
            case 5:
                return "🟠 {$name}"; // 橙色圆点 - 对应橙色背景 #e8a64e
            case 6:
                return "🔴 {$name}"; // 红色圆点 - 对应红色背景 #cb464a
            default:
                return "❓ {$name}"; // 未知等级
        }
    }

    /**
     * 根据平台格式化消息
     * @param string $message 原始消息
     * @param string $platform 平台名称
     * @return string 格式化后的消息
     */
    private function formatMessageForPlatform(string $message, string $platform): string
    {
        if ($platform === 'kook') {
            return $this->formatForKook($message);
        } else {
            return $this->formatForQQ($message);
        }
    }

    /**
     * 为 QQ 平台格式化消息（纯文本）
     */
    private function formatForQQ(string $message): string
    {
        // 移除 Markdown 语法
        return preg_replace(['/\*\*(.*?)\*\*/', '/`(.*?)`/'], ['$1', '$1'], $message);
    }

    /**
     * 为 KOOK 平台格式化消息（KMarkdown）
     * @param string $message 原始消息
     * @return string
     */
    private function formatForKook(string $message): string
    {
        $lines = explode("\n", $message);
        $formatted = [];

        foreach ($lines as $line) {
            $trimmed = trim($line);
            if (empty($trimmed)) {
                $formatted[] = '';
                continue;
            }

            // 处理主标题
            if (preg_match('/^💼\s+(.+)$/', $trimmed, $matches)) {
                $formatted[] = "# 💼 {$matches[1]}";
                continue;
            }

            // 处理统计信息（位置查询）
            if (preg_match('/^📊\s+共查询\s+(\d+)\s+个位置$/', $trimmed, $matches)) {
                $formatted[] = "**📊 共查询：** `{$matches[1]}个位置`";
                continue;
            }

            // 处理过滤提示
            if (preg_match('/^⚠️\s+技术中心已过滤周期≥16h项目$/', $trimmed)) {
                $formatted[] = "> ⚠️ **技术中心已过滤周期≥16h项目**";
                continue;
            }

            // 处理等级颜色说明（在结束提示中）
            if (preg_match('/颜色代表等级：(.+)/', $trimmed, $matches)) {
                $gradeInfo = $matches[1];
                $formatted[] = "> 🎨 **等级颜色**：{$gradeInfo}";
                continue;
            }

            // 处理统计信息（参数查询）
            if (preg_match('/^📊\s+共找到\s+(\d+)\s+个项目(.*)$/', $trimmed, $matches)) {
                $count = $matches[1];
                $conditions = $matches[2] ?? '';
                if (!empty($conditions)) {
                    $conditions = str_replace(' | 条件：', ' | **条件：** ', $conditions);
                }
                $formatted[] = "**📊 共找到：** `{$count}个项目`{$conditions}";
                continue;
            }

            // 处理列表标题
            if (preg_match('/^📋\s+(.+)$/', $trimmed, $matches)) {
                $formatted[] = "## 📋 {$matches[1]}";
                continue;
            }

            // 处理特勤处项目（带等级颜色）
            if (preg_match('/^•\s+(.+?)：(([⚫⚪🟢🔵🟣🟠🔴❓])\s+(.+))$/', $trimmed, $matches)) {
                $place = $matches[1];
                $colorDot = $matches[3]; // 颜色圆点
                $itemName = $matches[4]; // 物品名称
                $formatted[] = "- **{$place}**：{$colorDot} `{$itemName}`";
                continue;
            }

            // 处理没有等级颜色的项目（兼容性）
            if (preg_match('/^•\s+(.+?)：(.+)$/', $trimmed, $matches)) {
                $place = $matches[1];
                $name = $matches[2];
                $formatted[] = "- **{$place}**：`{$name}`";
                continue;
            }

            // 处理详细信息
            if (preg_match('/^\s+💰\s+利润：(.+?)\s+\|\s+⏱️\s+周期：(.+?)\s+\|\s+📈\s+时薪：(.+)$/', $trimmed, $matches)) {
                $profit = $matches[1];
                $period = $matches[2];
                $hourlyProfit = $matches[3];
                $formatted[] = "  💰 `{$profit}` | ⏱️ `{$period}` | 📈 `{$hourlyProfit}`";
                continue;
            }

            // 处理暂无数据
            if (preg_match('/^⚠️\s+暂无特勤处数据$/', $trimmed)) {
                $formatted[] = "> ⚠️ **暂无特勤处数据**";
                continue;
            }

            // 其他行保持原样
            $formatted[] = $trimmed;
        }

        // 在主要段落之间添加分隔线
        $result = implode("\n", $formatted);
        $result = preg_replace('/(\n## .+\n)/', "\n---$1", $result);
        $result = preg_replace('/^---\n/', '', $result); // 移除开头的分隔线

        return $result;
    }

    /**
     * 获取结束提示信息
     * @param string $platform 平台名称
     * @return string 结束提示
     */
    private function getEndingMessage(string $platform): string
    {
        try {
            // 加载插件配置文件
            $configPath = __DIR__ . '/../../config/plugins.php';
            if (!file_exists($configPath)) {
                return '';
            }

            $config = require $configPath;
            $endingMessages = $config['ending_messages'] ?? [];

            // 获取插件类名
            $className = 'SpecialOperationPlugin';

            // 先查找插件专属配置
            if (isset($endingMessages['plugins'][$className][$platform])) {
                $message = $endingMessages['plugins'][$className][$platform];
                return $this->formatEndingMessageForPlatform($message, $platform);
            }

            // 如果没有插件专属配置，使用全局默认配置
            if (isset($endingMessages['default'][$platform])) {
                $message = $endingMessages['default'][$platform];
                return $this->formatEndingMessageForPlatform($message, $platform);
            }

            return '';
        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to load custom ending message: " . $e->getMessage());
            return '';
        }
    }

    /**
     * 格式化自定义结束提示语
     * @param string $message 自定义提示语
     * @param string $platform 平台名称
     * @return string 格式化后的提示语
     */
    private function formatEndingMessageForPlatform(string $message, string $platform): string
    {
        if ($platform === 'kook') {
            return "\n\n---\n*{$message}*";
        } else {
            return "\n\n{$message}";
        }
    }

    /**
     * 检查数据库状态
     * @return array 数据库状态信息
     */
    private function checkDatabaseStatus(): array
    {
        try {
            // 检查表是否存在
            $tableExists = \think\facade\Db::query("SHOW TABLES LIKE 'ba_sjz_special_operations'");

            if (empty($tableExists)) {
                return [
                    'table_exists' => false,
                    'total_records' => 0,
                    'active_records' => 0,
                    'locations' => [],
                    'error' => 'Table ba_sjz_special_operations does not exist'
                ];
            }

            // 检查总记录数
            $totalRecords = \think\facade\Db::name('sjz_special_operations')->count();

            // 检查有效记录数
            $activeRecords = \think\facade\Db::name('sjz_special_operations')
                ->where('delete_time', null)
                ->where('status', 1)
                ->count();

            // 检查各位置的记录数
            $locations = [];
            $placeMap = [
                'workbench' => '工作台',
                'tech' => '技术中心',
                'pharmacy' => '制药台',
                'armory' => '防具'
            ];
            foreach ($placeMap as $placeValue => $placeName) {
                $count = \think\facade\Db::name('sjz_special_operations')
                    ->where('delete_time', null)
                    ->where('status', 1)
                    ->where('place', $placeValue)
                    ->count();
                $locations[$placeName] = $count;
            }

            // 检查字段结构
            $fields = \think\facade\Db::name('sjz_special_operations')->getFieldsType();

            return [
                'table_exists' => true,
                'total_records' => $totalRecords,
                'active_records' => $activeRecords,
                'locations' => $locations,
                'has_lirun_field' => isset($fields['lirun']),
                'fields' => array_keys($fields)
            ];

        } catch (\Exception $e) {
            return [
                'table_exists' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取插件信息
     */
    public function getInfo(): array
    {
        $info = parent::getInfo();
        $info['supported_commands'] = $this->supportedCommands;
        $info['usage_examples'] = [
            '/特勤处' => '查看各位置最高利润项目',
            '/特勤处 工作台' => '查看工作台的所有项目',
            '/特勤处 利润>=1000' => '查看利润大于等于1000的项目',
            '/特勤处 周期<=2' => '查看周期小于等于2小时的项目',
            '/特勤处 工作台 利润>=1000 周期<=2' => '组合查询条件'
        ];
        $info['grade_colors'] = [
            '⚫ 普通 (0级) - 对应前端灰色 #949595',
            '⚪ 优良 (1级) - 对应前端浅灰 #cdd5d5',
            '🟢 稀有 (2级) - 对应前端绿色 #188D14',
            '🔵 史诗 (3级) - 对应前端蓝色 #658BCE',
            '🟣 传说 (4级) - 对应前端紫色 #9b61c8',
            '🟠 神话 (5级) - 对应前端橙色 #e8a64e',
            '🔴 至尊 (6级) - 对应前端红色 #cb464a'
        ];
        return $info;
    }
}
