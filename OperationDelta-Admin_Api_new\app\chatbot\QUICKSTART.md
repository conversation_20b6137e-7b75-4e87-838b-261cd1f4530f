# Chatbot 快速开始指南

## 概述

本指南将帮助您快速了解和使用 Chatbot 模块，包括环境搭建、基本使用、开发新功能和部署。

## 环境要求

### 系统要求
- PHP >= 8.0
- MySQL >= 5.7
- ThinkPHP >= 6.0
- 支持 cURL 扩展

### 推荐环境
- PHP 8.1+
- MySQL 8.0+
- ThinkPHP 6.1+
- Redis (可选，用于缓存)

## 快速安装

### 1. 检查环境

```bash
# 检查 PHP 版本
php -v

# 检查 PHP 扩展
php -m | grep -E "(curl|pdo|json|mbstring)"

# 检查 MySQL 连接
mysql -u root -p -e "SELECT VERSION();"
```

### 2. 配置数据库

```sql
-- 创建数据库
CREATE DATABASE chatbot_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户表
CREATE TABLE chatbot_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    platform VARCHAR(50) NOT NULL,
    platform_user_id VARCHAR(100) NOT NULL,
    username VARCHAR(100),
    nickname VARCHAR(100),
    display_name VARCHAR(100),
    avatar TEXT,
    message_count INT DEFAULT 0,
    command_count INT DEFAULT 0,
    last_active TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_platform_user (platform, platform_user_id)
);

-- 创建群组表
CREATE TABLE chatbot_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    platform VARCHAR(50) NOT NULL,
    platform_group_id VARCHAR(100) NOT NULL,
    group_name VARCHAR(200),
    member_count INT DEFAULT 0,
    message_count INT DEFAULT 0,
    command_count INT DEFAULT 0,
    last_active TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_platform_group (platform, platform_group_id)
);

-- 创建插件表
CREATE TABLE chatbot_plugins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    version VARCHAR(20) NOT NULL,
    description TEXT,
    author VARCHAR(100),
    status ENUM('enabled', 'disabled', 'error') DEFAULT 'disabled',
    config JSON,
    dependencies JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_plugin_name (name)
);
```

### 3. 配置应用

```php
// config/database.php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'type' => 'mysql',
            'hostname' => '127.0.0.1',
            'database' => 'chatbot_db',
            'username' => 'root',
            'password' => 'your_password',
            'hostport' => '3306',
            'charset' => 'utf8mb4',
            'prefix' => '',
        ],
    ],
];
```

## 基本使用

### 1. 健康检查

```bash
# 使用 cURL 测试健康检查
curl -X GET "http://localhost/chatbot/health" \
  -H "Content-Type: application/json"
```

**预期响应：**
```json
{
    "code": 200,
    "message": "服务正常",
    "data": {
        "status": "ok",
        "timestamp": **********,
        "version": "1.0.0",
        "service": "chatbot-api"
    }
}
```

### 2. 测试命令处理

```bash
# 测试帮助命令
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/help",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'
```

**预期响应：**
```json
{
    "code": 200,
    "message": "命令执行成功",
    "data": {
        "success": true,
        "message": "📋 可用命令列表：\n\n• /help (帮助, h, ?)\n  显示帮助信息，列出所有可用命令\n\n• /ping (p)\n  测试连接状态\n\n💡 使用 /help [命令名] 查看具体命令的详细帮助",
        "type": "text",
        "timestamp": **********
    }
}
```

### 3. 测试其他命令

```bash
# 测试 ping 命令
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/ping",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'

# 测试状态命令
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/status",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'

# 测试版本命令
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/version",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'
```

## 插件使用

### 1. 启用天气插件

```bash
# 启用天气插件
curl -X POST "http://localhost/chatbot/plugins/weather/enable" \
  -H "Content-Type: application/json"
```

### 2. 测试天气插件

```bash
# 查询天气
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/weather 北京",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'

# 查看支持的城市
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/cities",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'
```

### 3. 测试游戏插件

```bash
# 开始猜数字游戏
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/game guess",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'

# 开始石头剪刀布
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/game rps",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'
```

## 开发新功能

### 1. 创建新命令

#### 步骤 1：创建命令类

```php
<?php
// app/chatbot/commands/core/TimeCommand.php

namespace app\chatbot\commands\core;

use app\chatbot\commands\AbstractCommand;

class TimeCommand extends AbstractCommand
{
    protected string $name = 'time';
    protected array $aliases = ['时间', 't'];
    protected string $description = '显示当前时间';
    protected string $usage = '/time - 显示当前时间';

    protected function handle(array $args, array $context): array
    {
        $currentTime = date('Y-m-d H:i:s');
        $timezone = date_default_timezone_get();
        
        $message = "🕐 当前时间：{$currentTime}\n";
        $message .= "🌍 时区：{$timezone}";
        
        return $this->formatResponse(
            true,
            $message,
            'text'
        );
    }
}
```

#### 步骤 2：注册命令

```php
// app/chatbot/commands/CommandFactory.php
// 在 initialize() 方法中添加：

public static function initialize(): void
{
    // 注册核心命令
    self::$commands['help'] = \app\chatbot\commands\core\HelpCommand::class;
    self::$commands['ping'] = \app\chatbot\commands\core\PingCommand::class;
    self::$commands['status'] = \app\chatbot\commands\core\StatusCommand::class;
    self::$commands['version'] = \app\chatbot\commands\core\VersionCommand::class;
    self::$commands['time'] = \app\chatbot\commands\core\TimeCommand::class; // 新增
}
```

#### 步骤 3：测试命令

```bash
# 测试新命令
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/time",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'
```

### 2. 创建新插件

#### 步骤 1：创建插件目录

```bash
mkdir -p app/chatbot/plugins/custom/calculator
```

#### 步骤 2：创建插件类

```php
<?php
// app/chatbot/plugins/custom/calculator/CalculatorPlugin.php

namespace app\chatbot\plugins\custom\calculator;

use app\chatbot\plugins\base\AbstractPlugin;

class CalculatorPlugin extends AbstractPlugin
{
    protected string $name = 'calculator';
    protected string $version = '1.0.0';
    protected string $description = '简单计算器插件';
    protected string $author = '开发者';
    protected array $dependencies = [];

    protected function onInitialize(): bool
    {
        // 初始化逻辑
        return true;
    }

    protected function onEnable(): bool
    {
        // 启用逻辑
        return true;
    }

    protected function onDisable(): bool
    {
        // 禁用逻辑
        return true;
    }

    protected function onUninstall(): bool
    {
        // 卸载逻辑
        return true;
    }

    public function handleCommand(string $command, string $args, array $context): ?array
    {
        if ($command === 'calc') {
            return $this->calculate($args);
        }
        
        return null;
    }

    private function calculate(string $expression): array
    {
        try {
            // 简单的数学表达式计算
            $expression = trim($expression);
            
            // 安全检查：只允许数字、运算符和括号
            if (!preg_match('/^[\d\+\-\*\/\(\)\s\.]+$/', $expression)) {
                return [
                    'success' => false,
                    'message' => '❌ 表达式包含非法字符',
                    'type' => 'text'
                ];
            }
            
            // 计算结果
            $result = eval("return $expression;");
            
            return [
                'success' => true,
                'message' => "🧮 计算结果：{$expression} = {$result}",
                'type' => 'text'
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '❌ 计算错误：' . $e->getMessage(),
                'type' => 'text'
            ];
        }
    }
}
```

#### 步骤 3：创建配置文件

```php
<?php
// app/chatbot/plugins/custom/calculator/config.php

return [
    'name' => 'calculator',
    'version' => '1.0.0',
    'description' => '简单计算器插件',
    'author' => '开发者',
    'dependencies' => [],
    'enabled' => true
];
```

#### 步骤 4：启用插件

```bash
# 启用计算器插件
curl -X POST "http://localhost/chatbot/plugins/calculator/enable" \
  -H "Content-Type: application/json"
```

#### 步骤 5：测试插件

```bash
# 测试计算器插件
curl -X POST "http://localhost/chatbot/command/process" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "/calc 2 + 3 * 4",
    "platform": "kook",
    "user_id": "123456",
    "username": "测试用户"
  }'
```

## 调试和故障排除

### 1. 启用调试模式

```php
// config/app.php
return [
    'debug' => true,
    'log' => [
        'level' => 'debug',
    ],
];
```

### 2. 查看日志

```bash
# 查看应用日志
tail -f runtime/log/app.log

# 查看错误日志
tail -f runtime/log/error.log
```

### 3. 常见问题

#### 问题 1：命令未找到
**症状：** 返回 "命令未找到" 错误
**解决方案：**
1. 检查命令是否正确注册
2. 确认命令名称拼写
3. 重启应用

```bash
# 检查命令列表
curl -X GET "http://localhost/chatbot/commands" \
  -H "Content-Type: application/json"
```

#### 问题 2：插件加载失败
**症状：** 插件启用失败
**解决方案：**
1. 检查插件依赖
2. 确认插件文件权限
3. 查看插件加载日志

```bash
# 检查插件列表
curl -X GET "http://localhost/chatbot/plugins" \
  -H "Content-Type: application/json"
```

#### 问题 3：数据库连接错误
**症状：** 数据库相关错误
**解决方案：**
1. 检查数据库配置
2. 确认数据库服务运行
3. 验证数据库用户权限

```bash
# 测试数据库连接
php -r "
try {
    \$pdo = new PDO('mysql:host=localhost;dbname=chatbot_db', 'root', 'password');
    echo '数据库连接成功\n';
} catch (PDOException \$e) {
    echo '数据库连接失败: ' . \$e->getMessage() . '\n';
}
"
```

## 性能优化

### 1. 启用缓存

```php
// config/cache.php
return [
    'default' => 'redis',
    'stores' => [
        'redis' => [
            'type' => 'redis',
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => '',
            'select' => 0,
        ],
    ],
];
```

### 2. 数据库优化

```sql
-- 添加索引
CREATE INDEX idx_platform_user ON chatbot_users(platform, platform_user_id);
CREATE INDEX idx_last_active ON chatbot_users(last_active);
CREATE INDEX idx_plugin_status ON chatbot_plugins(status);
```

### 3. 监控性能

```bash
# 查看系统状态
curl -X GET "http://localhost/chatbot/stats" \
  -H "Content-Type: application/json"
```

## 部署

### 1. 生产环境配置

```php
// config/app.php
return [
    'debug' => false,
    'log' => [
        'level' => 'error',
    ],
    'cache' => [
        'default' => 'redis',
    ],
];
```

### 2. 使用 Docker 部署

```bash
# 构建镜像
docker build -t chatbot-api .

# 运行容器
docker run -d \
  --name chatbot-api \
  -p 9000:9000 \
  -e DB_HOST=mysql \
  -e DB_DATABASE=chatbot_db \
  -e DB_USERNAME=root \
  -e DB_PASSWORD=password \
  chatbot-api
```

### 3. 使用 Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f chatbot-api

# 停止服务
docker-compose down
```

## 下一步

1. **阅读完整文档**
   - [README.md](README.md) - 模块概述
   - [API.md](API.md) - API 接口文档
   - [ARCHITECTURE.md](ARCHITECTURE.md) - 架构设计文档

2. **探索示例**
   - 查看 `commands/core/` 目录中的核心命令
   - 查看 `plugins/examples/` 目录中的示例插件

3. **开发实践**
   - 创建自定义命令
   - 开发功能插件
   - 集成第三方服务

4. **贡献代码**
   - 遵循代码规范
   - 编写测试用例
   - 更新文档

## 获取帮助

- **文档**: 查看项目文档
- **问题**: 提交 Issue
- **讨论**: 参与社区讨论
- **邮件**: 联系开发团队

祝您使用愉快！
