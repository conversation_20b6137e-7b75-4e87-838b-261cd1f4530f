<?php

namespace app\chatbot\commands;

use app\chatbot\service\ChatbotCacheManager;
use think\facade\Log;

/**
 * 命令工厂类
 * 负责命令的注册、查找和实例化
 */
class CommandFactory
{
    /**
     * 已注册的命令映射
     * @var array
     */
    private static array $commands = [];

    /**
     * 命令别名映射
     * @var array
     */
    private static array $aliases = [];

    /**
     * 是否已初始化
     * @var bool
     */
    private static bool $initialized = false;

    /**
     * 缓存管理器实例
     * @var ChatbotCacheManager|null
     */
    private static ?ChatbotCacheManager $cacheManager = null;

    /**
     * 初始化命令工厂
     */
    public static function initialize(): void
    {
        if (self::$initialized) {
            return;
        }

        // 初始化缓存管理器
        self::$cacheManager = new ChatbotCacheManager();

        // 注册核心命令
        self::registerCommands([
            \app\chatbot\commands\core\HelpCommand::class,
            \app\chatbot\commands\core\PingCommand::class,
            \app\chatbot\commands\core\StatusCommand::class,
            \app\chatbot\commands\core\VersionCommand::class,
        ]);

        self::$initialized = true;
        Log::info('CommandFactory initialized with ' . count(self::$commands) . ' commands');
    }

    /**
     * 注册命令类
     * @param string $commandClass 命令类名
     * @return bool 是否注册成功
     */
    public static function registerCommand(string $commandClass): bool
    {
        try {
            if (!class_exists($commandClass)) {
                Log::error("Command class not found: {$commandClass}");
                return false;
            }

            $command = new $commandClass();
            if (!$command instanceof CommandInterface) {
                Log::error("Command class must implement CommandInterface: {$commandClass}");
                return false;
            }

            $name = $command->getName();
            if (empty($name)) {
                Log::error("Command name cannot be empty: {$commandClass}");
                return false;
            }

            // 注册主命令
            self::$commands[$name] = $commandClass;

            // 注册别名
            $aliases = $command->getAliases();
            foreach ($aliases as $alias) {
                if (!empty($alias) && $alias !== $name) {
                    self::$aliases[$alias] = $name;
                }
            }

            Log::info("Command registered: {$name} ({$commandClass})");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to register command {$commandClass}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量注册命令
     * @param array $commandClasses 命令类名数组
     * @return array 注册结果
     */
    public static function registerCommands(array $commandClasses): array
    {
        $results = [];
        foreach ($commandClasses as $commandClass) {
            $results[$commandClass] = self::registerCommand($commandClass);
        }
        return $results;
    }

    /**
     * 获取命令实例
     * @param string $commandName 命令名称
     * @return CommandInterface|null 命令实例
     */
    public static function getCommand(string $commandName): ?CommandInterface
    {
        self::initialize();

        // 查找主命令
        if (isset(self::$commands[$commandName])) {
            $commandClass = self::$commands[$commandName];
            return new $commandClass();
        }

        // 查找别名
        if (isset(self::$aliases[$commandName])) {
            $mainCommand = self::$aliases[$commandName];
            if (isset(self::$commands[$mainCommand])) {
                $commandClass = self::$commands[$mainCommand];
                return new $commandClass();
            }
        }

        return null;
    }

    /**
     * 检查命令是否存在
     * @param string $commandName 命令名称
     * @return bool 是否存在
     */
    public static function hasCommand(string $commandName): bool
    {
        self::initialize();
        return isset(self::$commands[$commandName]) || isset(self::$aliases[$commandName]);
    }

    /**
     * 获取所有已注册的命令名称
     * @return array 命令名称数组
     */
    public static function getCommandNames(): array
    {
        self::initialize();
        return array_keys(self::$commands);
    }

    /**
     * 获取所有已注册的命令别名
     * @return array 别名映射数组
     */
    public static function getAliases(): array
    {
        self::initialize();
        return self::$aliases;
    }

    /**
     * 获取命令列表（包含详细信息）
     * @return array 命令信息数组
     */
    public static function getCommandList(): array
    {
        self::initialize();
        
        // 尝试从缓存获取
        $cachedList = self::$cacheManager->get('list', ChatbotCacheManager::TYPE_COMMAND_LIST);
        if ($cachedList !== null) {
            return $cachedList;
        }
        
        $list = [];

        foreach (self::$commands as $name => $commandClass) {
            try {
                $command = new $commandClass();
                $list[$name] = [
                    'name' => $command->getName(),
                    'aliases' => $command->getAliases(),
                    'description' => $command->getDescription(),
                    'usage' => $command->getUsage(),
                    'class' => $commandClass
                ];
            } catch (\Exception $e) {
                Log::error("Failed to get command info for {$name}: " . $e->getMessage());
            }
        }

        // 缓存到 Redis
        self::$cacheManager->set('list', $list, ChatbotCacheManager::TYPE_COMMAND_LIST);
        
        return $list;
    }

    /**
     * 清除所有注册的命令
     */
    public static function clear(): void
    {
        self::$commands = [];
        self::$aliases = [];
        self::$initialized = false;
        Log::info('CommandFactory cleared');
    }

    /**
     * 获取注册统计信息
     * @return array 统计信息
     */
    public static function getStats(): array
    {
        self::initialize();
        return [
            'total_commands' => count(self::$commands),
            'total_aliases' => count(self::$aliases),
            'initialized' => self::$initialized
        ];
    }
}
