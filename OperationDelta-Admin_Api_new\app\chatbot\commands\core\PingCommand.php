<?php

namespace app\chatbot\commands\core;

use app\chatbot\commands\AbstractCommand;

/**
 * Ping 命令处理器
 * 用于测试机器人响应
 */
class PingCommand extends AbstractCommand
{
    protected string $name = 'ping';
    protected array $aliases = ['p'];
    protected string $description = '测试机器人响应，检查连接状态';
    protected string $usage = '/ping - 返回 pong 响应';

    /**
     * 处理 ping 命令
     * @param array $args 命令参数
     * @param array $context 执行上下文
     * @return array
     */
    protected function handle(array $args, array $context): array
    {
        $platform = $context['platform'] ?? 'unknown';
        $timestamp = date('Y-m-d H:i:s');
        
        $message = "🏓 Pong! 📱 {$platform} ⏰ {$timestamp} 💚 在线";

        return $this->formatResponse(
            true,
            $message,
            'text',
            [],
            $context
        );
    }

    /**
     * 验证命令参数
     * @param array $args 参数
     * @param array $context 上下文
     * @return array
     */
    protected function validateArgs(array $args, array $context): array
    {
        // ping 命令不接受任何参数
        if (!empty($args)) {
            return [
                'valid' => false,
                'message' => '❌ ping 命令不需要参数。用法：/ping'
            ];
        }

        return ['valid' => true, 'message' => ''];
    }

}
