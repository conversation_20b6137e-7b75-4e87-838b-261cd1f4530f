<?php

namespace app\chatbot\service;

use think\facade\Cache;
use think\facade\Log;

/**
 * Chatbot 缓存管理器
 * 专门用于管理 chatbot 模块的缓存数据
 */
class ChatbotCacheManager
{
    // 缓存类型常量
    const TYPE_PLUGIN_STATUS = 'plugin_status';   // 插件状态 - 长期缓存
    const TYPE_PLUGIN_LIST = 'plugin_list';       // 插件列表 - 短期缓存
    const TYPE_COMMAND_LIST = 'command_list';     // 命令列表 - 短期缓存
    const TYPE_USER_SESSION = 'user_session';     // 用户会话 - 短期缓存
    const TYPE_SYSTEM_CONFIG = 'system_config';   // 系统配置 - 长期缓存

    /**
     * 缓存前缀
     */
    private const CACHE_PREFIX = 'chatbot:';

    /**
     * Redis 数据库编号
     */
    private const REDIS_DB = 1;

    /**
     * 获取缓存 TTL
     * @param string $type 缓存类型
     * @return int TTL（秒）
     */
    private function getCacheTTL(string $type): int
    {
        switch ($type) {
            case self::TYPE_PLUGIN_STATUS:
                return 86400; // 24小时
            case self::TYPE_PLUGIN_LIST:
                return 300;   // 5分钟
            case self::TYPE_COMMAND_LIST:
                return 600;   // 10分钟
            case self::TYPE_USER_SESSION:
                return 1800;  // 30分钟
            case self::TYPE_SYSTEM_CONFIG:
                return 3600;  // 1小时
            default:
                return 3600;  // 默认1小时
        }
    }

    /**
     * 构建缓存键
     * @param string $key 键名
     * @param string $type 缓存类型
     * @return string 完整的缓存键
     */
    private function buildCacheKey(string $key, string $type): string
    {
        return self::CACHE_PREFIX . "{$type}:{$key}";
    }

    /**
     * 记录缓存操作日志
     * @param string $operation 操作类型
     * @param string $key 缓存键
     * @param string $type 缓存类型
     * @param int $ttl TTL
     */
    private function logCacheOperation(string $operation, string $key, string $type, int $ttl = 0): void
    {
        $message = "Chatbot Cache {$operation}: {$key} (Type: {$type}";
        if ($ttl > 0) {
            $message .= ", TTL: {$ttl}s";
        }
        $message .= ")";
        
        Log::info($message);
    }

    /**
     * 设置缓存
     * @param string $key 键名
     * @param mixed $value 值
     * @param string $type 缓存类型
     * @return bool 是否成功
     */
    public function set(string $key, $value, string $type): bool
    {
        try {
            $fullKey = $this->buildCacheKey($key, $type);
            $ttl = $this->getCacheTTL($type);
            
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            
            // 对复杂数据类型进行 JSON 编码
            $encodedValue = is_array($value) || is_object($value) ? json_encode($value) : $value;
            $result = $redis->setex($fullKey, $ttl, $encodedValue);
            
            if ($result) {
                $this->logCacheOperation('SET', $fullKey, $type, $ttl);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error("Chatbot cache set failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 获取缓存
     * @param string $key 键名
     * @param string $type 缓存类型
     * @param mixed $default 默认值
     * @return mixed 缓存值或默认值
     */
    public function get(string $key, string $type, $default = null)
    {
        try {
            $fullKey = $this->buildCacheKey($key, $type);
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            $value = $redis->get($fullKey);
            
            if ($value !== false) {
                $this->logCacheOperation('HIT', $fullKey, $type);
                
                // 尝试 JSON 解码，如果失败则返回原始值
                $decodedValue = json_decode($value, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    return $decodedValue;
                }
                return $value;
            } else {
                $this->logCacheOperation('MISS', $fullKey, $type);
                return $default;
            }
        } catch (\Exception $e) {
            Log::error("Chatbot cache get failed: {$e->getMessage()}");
            return $default;
        }
    }

    /**
     * 删除缓存
     * @param string $key 键名
     * @param string $type 缓存类型
     * @return bool 是否成功
     */
    public function delete(string $key, string $type): bool
    {
        try {
            $fullKey = $this->buildCacheKey($key, $type);
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            $result = $redis->del($fullKey) > 0;
            
            if ($result) {
                $this->logCacheOperation('DELETE', $fullKey, $type);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error("Chatbot cache delete failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 检查缓存是否存在
     * @param string $key 键名
     * @param string $type 缓存类型
     * @return bool 是否存在
     */
    public function exists(string $key, string $type): bool
    {
        try {
            $fullKey = $this->buildCacheKey($key, $type);
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            return $redis->exists($fullKey);
        } catch (\Exception $e) {
            Log::error("Chatbot cache exists failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 设置哈希表字段
     * @param string $key 键名
     * @param string $field 字段名
     * @param mixed $value 值
     * @param string $type 缓存类型
     * @return bool 是否成功
     */
    public function hSet(string $key, string $field, $value, string $type): bool
    {
        try {
            $fullKey = $this->buildCacheKey($key, $type);
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            $result = $redis->hSet($fullKey, $field, $value) !== false;
            
            if ($result) {
                // 设置过期时间
                $ttl = $this->getCacheTTL($type);
                $redis->expire($fullKey, $ttl);
                $this->logCacheOperation('HSET', $fullKey, $type, $ttl);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error("Chatbot cache hSet failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 获取哈希表字段
     * @param string $key 键名
     * @param string $field 字段名
     * @param string $type 缓存类型
     * @param mixed $default 默认值
     * @return mixed 字段值或默认值
     */
    public function hGet(string $key, string $field, string $type, $default = null)
    {
        try {
            $fullKey = $this->buildCacheKey($key, $type);
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            $value = $redis->hGet($fullKey, $field);
            
            if ($value !== false) {
                $this->logCacheOperation('HGET', $fullKey, $type);
                return $value;
            } else {
                $this->logCacheOperation('HMISS', $fullKey, $type);
                return $default;
            }
        } catch (\Exception $e) {
            Log::error("Chatbot cache hGet failed: {$e->getMessage()}");
            return $default;
        }
    }

    /**
     * 获取哈希表所有字段
     * @param string $key 键名
     * @param string $type 缓存类型
     * @return array 所有字段
     */
    public function hGetAll(string $key, string $type): array
    {
        try {
            $fullKey = $this->buildCacheKey($key, $type);
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            $data = $redis->hGetAll($fullKey);
            
            if (!empty($data)) {
                $this->logCacheOperation('HGETALL', $fullKey, $type);
            }
            
            return $data ?: [];
        } catch (\Exception $e) {
            Log::error("Chatbot cache hGetAll failed: {$e->getMessage()}");
            return [];
        }
    }

    /**
     * 删除哈希表字段
     * @param string $key 键名
     * @param string $field 字段名
     * @param string $type 缓存类型
     * @return bool 是否成功
     */
    public function hDel(string $key, string $field, string $type): bool
    {
        try {
            $fullKey = $this->buildCacheKey($key, $type);
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            $result = $redis->hDel($fullKey, $field) > 0;
            
            if ($result) {
                $this->logCacheOperation('HDEL', $fullKey, $type);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error("Chatbot cache hDel failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 清除指定类型的所有缓存
     * @param string $type 缓存类型
     * @return bool 是否成功
     */
    public function clearByType(string $type): bool
    {
        try {
            $pattern = self::CACHE_PREFIX . "{$type}:*";
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            $keys = $redis->keys($pattern);
            
            if (!empty($keys)) {
                $result = $redis->del($keys) > 0;
                Log::info("Chatbot cache CLEAR: Type {$type}, cleared " . count($keys) . " keys");
                return $result;
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error("Chatbot cache clearByType failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 清除所有 chatbot 缓存
     * @return bool 是否成功
     */
    public function clearAll(): bool
    {
        try {
            $pattern = self::CACHE_PREFIX . "*";
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            $keys = $redis->keys($pattern);
            
            if (!empty($keys)) {
                $result = $redis->del($keys) > 0;
                Log::info("Chatbot cache CLEAR ALL: cleared " . count($keys) . " keys");
                return $result;
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error("Chatbot cache clearAll failed: {$e->getMessage()}");
            return false;
        }
    }

    /**
     * 获取缓存统计信息
     * @return array 统计信息
     */
    public function getStats(): array
    {
        try {
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            
            $stats = [
                'memory_usage' => $redis->info('memory')['used_memory_human'] ?? 'unknown',
                'total_keys' => $redis->dbSize(),
                'connected' => true
            ];
            
            // 统计各类型缓存数量
            foreach ([
                self::TYPE_PLUGIN_STATUS,
                self::TYPE_PLUGIN_LIST,
                self::TYPE_COMMAND_LIST,
                self::TYPE_USER_SESSION,
                self::TYPE_SYSTEM_CONFIG
            ] as $type) {
                $pattern = self::CACHE_PREFIX . "{$type}:*";
                $stats["{$type}_keys"] = count($redis->keys($pattern));
            }
            
            return $stats;
        } catch (\Exception $e) {
            return [
                'memory_usage' => 'unknown',
                'total_keys' => 0,
                'connected' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 检查 Redis 是否可用
     * @return bool 是否可用
     */
    public function isAvailable(): bool
    {
        try {
            $redis = Cache::store('redis')->handler();
            $redis->select(self::REDIS_DB);
            return $redis->ping() === '+PONG';
        } catch (\Exception $e) {
            return false;
        }
    }
}
