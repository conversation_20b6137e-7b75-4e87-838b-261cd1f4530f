<?php

namespace app\chatbot\commands\core;

use app\chatbot\commands\AbstractCommand;

/**
 * 版本命令处理器
 * 显示机器人版本信息
 */
class VersionCommand extends AbstractCommand
{
    protected string $name = 'version';
    protected array $aliases = ['版本', 'ver', 'v'];
    protected string $description = '显示机器人版本信息';
    protected string $usage = '/version - 查看版本信息';

    /**
     * 处理版本命令
     * @param array $args 命令参数
     * @param array $context 执行上下文
     * @return array
     */
    protected function handle(array $args, array $context): array
    {
        $platform = $context['platform'] ?? 'unknown';
        $timestamp = date('Y-m-d H:i:s');
        
        $message = "📋 Chatbot API v1.0.0 📱 {$platform} ⏰ {$timestamp}\n";
        $message .= "🛠️ ThinkPHP + PHP 8.0+ + MySQL 📅 2024-12-19\n";
        $message .= "📝 支持QQ/KOOK双平台，模块化命令系统，插件化架构";

        return $this->formatResponse(
            true,
            $message,
            'text',
            [],
            $context
        );
    }

    /**
     * 验证命令参数
     * @param array $args 参数
     * @param array $context 上下文
     * @return array
     */
    protected function validateArgs(array $args, array $context): array
    {
        // version 命令不接受任何参数
        if (!empty($args)) {
            return [
                'valid' => false,
                'message' => '❌ version 命令不需要参数。用法：/version'
            ];
        }

        return ['valid' => true, 'message' => ''];
    }
}
