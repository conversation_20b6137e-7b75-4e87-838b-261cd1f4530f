<?php

namespace app\chatbot\plugins\manager;

use app\chatbot\plugins\base\PluginInterface;
use app\chatbot\service\ChatbotCacheManager;
use think\facade\Log;

/**
 * 插件管理器
 * 负责插件的注册、加载、启用、禁用等管理功能
 */
class PluginManager
{
    /**
     * 已注册的插件映射
     * @var array
     */
    private static array $plugins = [];

    /**
     * 插件配置
     * @var array
     */
    private static array $config = [];

    /**
     * 是否已初始化
     * @var bool
     */
    private static bool $initialized = false;

    /**
     * 插件目录
     * @var string
     */
    private static string $pluginDir = '';

    /**
     * 插件状态缓存（内存备用）
     * @var array
     */
    private static array $pluginStatus = [];
    
    /**
     * 缓存管理器实例
     */
    private static ?ChatbotCacheManager $cacheManager = null;

    /**
     * 初始化插件管理器
     */
    public static function initialize(): void
    {
        if (self::$initialized) {
            return;
        }

        // 初始化缓存管理器
        self::$cacheManager = new ChatbotCacheManager();

        self::$pluginDir = __DIR__ . '/../';
        self::loadConfig();
        self::loadPlugins();

        self::$initialized = true;
        Log::info('PluginManager initialized with ' . count(self::$plugins) . ' plugins');
    }

    /**
     * 加载插件配置
     */
    private static function loadConfig(): void
    {
        $configFile = self::$pluginDir . 'config/plugins.php';
        if (file_exists($configFile)) {
            self::$config = include $configFile;
        } else {
            self::$config = [
                'auto_load' => true,
                'enable_cache' => false,
                'cache_ttl' => 3600
            ];
        }
    }

    /**
     * 加载插件
     */
    private static function loadPlugins(): void
    {
        // 自动扫描插件目录
        if (self::$config['auto_load'] ?? true) {
            self::scanPluginDirectory();
        }

        // 加载配置中的插件
        $configuredPlugins = self::$config['plugins'] ?? [];
        foreach ($configuredPlugins as $pluginClass) {
            self::registerPlugin($pluginClass);
        }
    }

    /**
     * 扫描插件目录
     */
    private static function scanPluginDirectory(): void
    {
        $pluginDirs = [
            self::$pluginDir . 'examples/',
            self::$pluginDir . 'custom/'
        ];

        foreach ($pluginDirs as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '*Plugin.php');
                foreach ($files as $file) {
                    $className = 'app\\chatbot\\plugins\\' . basename(dirname($file)) . '\\' . basename($file, '.php');
                    self::registerPlugin($className);
                }
            }
        }
    }

    /**
     * 注册插件
     * @param string $pluginClass 插件类名
     * @return bool 是否注册成功
     */
    public static function registerPlugin(string $pluginClass): bool
    {
        try {
            if (!class_exists($pluginClass)) {
                Log::error("Plugin class not found: {$pluginClass}");
                return false;
            }

            $plugin = new $pluginClass();
            if (!$plugin instanceof PluginInterface) {
                Log::error("Plugin class must implement PluginInterface: {$pluginClass}");
                return false;
            }

            $name = $plugin->getName();
            if (empty($name)) {
                Log::error("Plugin name cannot be empty: {$pluginClass}");
                return false;
            }

            // 检查依赖
            if (!self::checkDependencies($plugin)) {
                Log::error("Plugin dependencies not met: {$name}");
                return false;
            }

            self::$plugins[$name] = $pluginClass;
            Log::info("Plugin registered: {$name} ({$pluginClass})");
            return true;

        } catch (\Exception $e) {
            Log::error("Failed to register plugin {$pluginClass}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查插件依赖
     * @param PluginInterface $plugin
     * @return bool
     */
    private static function checkDependencies(PluginInterface $plugin): bool
    {
        $dependencies = $plugin->getDependencies();
        foreach ($dependencies as $dependency) {
            if (!self::checkDependency($dependency)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 检查单个依赖
     * @param string $dependency
     * @return bool
     */
    private static function checkDependency(string $dependency): bool
    {
        // 检查 PHP 扩展
        if (strpos($dependency, 'ext:') === 0) {
            $extName = substr($dependency, 4);
            return extension_loaded($extName);
        }

        // 检查其他插件
        if (strpos($dependency, 'plugin:') === 0) {
            $pluginName = substr($dependency, 7);
            return isset(self::$plugins[$pluginName]);
        }

        // 检查 PHP 版本
        if (strpos($dependency, 'php:') === 0) {
            $requiredVersion = substr($dependency, 4);
            return version_compare(PHP_VERSION, $requiredVersion, '>=');
        }

        return true;
    }

    /**
     * 获取插件实例
     * @param string $pluginName 插件名称
     * @return PluginInterface|null
     */
    public static function getPlugin(string $pluginName): ?PluginInterface
    {
        self::initialize();

        if (!isset(self::$plugins[$pluginName])) {
            return null;
        }

        $pluginClass = self::$plugins[$pluginName];
        return new $pluginClass();
    }

    /**
     * 启用插件
     * @param string $pluginName 插件名称
     * @return bool 是否启用成功
     */
    public static function enablePlugin(string $pluginName): bool
    {
        $plugin = self::getPlugin($pluginName);
        if (!$plugin) {
            Log::error("Plugin not found: {$pluginName}");
            return false;
        }

        $result = $plugin->enable();
        if ($result) {
            // 更新内存缓存
            self::$pluginStatus[$pluginName] = 'enabled';
            
            // 更新缓存
            self::$cacheManager->hSet('status', $pluginName, 'enabled', ChatbotCacheManager::TYPE_PLUGIN_STATUS);
            
            Log::info("Plugin enabled and cached: {$pluginName}");
        }
        return $result;
    }

    /**
     * 禁用插件
     * @param string $pluginName 插件名称
     * @return bool 是否禁用成功
     */
    public static function disablePlugin(string $pluginName): bool
    {
        $plugin = self::getPlugin($pluginName);
        if (!$plugin) {
            Log::error("Plugin not found: {$pluginName}");
            return false;
        }

        $result = $plugin->disable();
        if ($result) {
            // 更新内存缓存
            self::$pluginStatus[$pluginName] = 'disabled';
            
            // 更新缓存
            self::$cacheManager->hSet('status', $pluginName, 'disabled', ChatbotCacheManager::TYPE_PLUGIN_STATUS);
            
            Log::info("Plugin disabled and cached: {$pluginName}");
        }
        return $result;
    }

    /**
     * 卸载插件
     * @param string $pluginName 插件名称
     * @return bool 是否卸载成功
     */
    public static function uninstallPlugin(string $pluginName): bool
    {
        $plugin = self::getPlugin($pluginName);
        if (!$plugin) {
            Log::error("Plugin not found: {$pluginName}");
            return false;
        }

        $result = $plugin->uninstall();
        if ($result) {
            unset(self::$plugins[$pluginName]);
        }

        return $result;
    }

    /**
     * 获取所有插件列表
     * @return array
     */
    public static function getPluginList(): array
    {
        self::initialize();
        
        // 尝试从缓存获取
        $cachedList = self::$cacheManager->get('list', ChatbotCacheManager::TYPE_PLUGIN_LIST);
        if ($cachedList !== null) {
            return $cachedList;
        }
        
        $list = [];

        foreach (self::$plugins as $name => $pluginClass) {
            try {
                $plugin = new $pluginClass();
                $status = self::getPluginStatus($name);
                $list[$name] = [
                    'name' => $plugin->getName(),
                    'version' => $plugin->getVersion(),
                    'description' => $plugin->getDescription(),
                    'author' => $plugin->getAuthor(),
                    'dependencies' => $plugin->getDependencies(),
                    'status' => $status,
                    'available' => $plugin->isAvailable() && $status === 'enabled',
                    'class' => $pluginClass
                ];
            } catch (\Exception $e) {
                Log::error("Failed to get plugin info for {$name}: " . $e->getMessage());
            }
        }

        // 缓存到 Redis
        self::$cacheManager->set('list', $list, ChatbotCacheManager::TYPE_PLUGIN_LIST);
        
        return $list;
    }
    
    /**
     * 获取插件状态（优先从缓存获取）
     * @param string $pluginName 插件名称
     * @return string
     */
    private static function getPluginStatus(string $pluginName): string
    {
        // 优先从缓存获取
        $status = self::$cacheManager->hGet('status', $pluginName, ChatbotCacheManager::TYPE_PLUGIN_STATUS);
        if ($status !== null) {
            return $status;
        }
        
        // 从内存缓存获取
        return self::$pluginStatus[$pluginName] ?? 'disabled';
    }

    /**
     * 获取已启用的插件列表
     * @return array
     */
    public static function getEnabledPlugins(): array
    {
        $allPlugins = self::getPluginList();
        return array_filter($allPlugins, function($plugin) {
            return $plugin['status'] === 'enabled';
        });
    }

    /**
     * 检查插件是否存在
     * @param string $pluginName 插件名称
     * @return bool
     */
    public static function hasPlugin(string $pluginName): bool
    {
        self::initialize();
        return isset(self::$plugins[$pluginName]);
    }

    /**
     * 获取插件统计信息
     * @return array
     */
    public static function getStats(): array
    {
        self::initialize();
        $enabledCount = 0;
        $disabledCount = 0;

        foreach (self::$plugins as $name => $pluginClass) {
            $status = self::getPluginStatus($name);
            if ($status === 'enabled') {
                $enabledCount++;
            } else {
                $disabledCount++;
            }
        }

        $redisStats = self::$cacheManager->getStats();

        return [
            'total_plugins' => count(self::$plugins),
            'enabled_plugins' => $enabledCount,
            'disabled_plugins' => $disabledCount,
            'initialized' => self::$initialized,
            'redis' => $redisStats
        ];
    }

    /**
     * 清除所有插件
     */
    public static function clear(): void
    {
        self::$plugins = [];
        self::$config = [];
        self::$initialized = false;
        
        // 清除缓存
        self::$cacheManager->clearByType(ChatbotCacheManager::TYPE_PLUGIN_STATUS);
        self::$cacheManager->clearByType(ChatbotCacheManager::TYPE_PLUGIN_LIST);
        
        Log::info('PluginManager cleared');
    }

    /**
     * 重新加载插件
     */
    public static function reload(): void
    {
        self::clear();
        self::initialize();
        Log::info('PluginManager reloaded');
    }
}
