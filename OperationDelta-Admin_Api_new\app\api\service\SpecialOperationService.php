<?php

namespace app\api\service;

use think\facade\Db;
use think\facade\Log;
use app\api\service\CacheManager;

/**
 * 特勤处服务类
 * 提供特勤处相关的业务逻辑
 */
class SpecialOperationService
{
    /**
     * 获取特勤处列表数据
     * 从SpecialOperation控制器的fetchSpecialOperationListData方法提取的业务逻辑
     * 
     * @param array $params 查询参数
     * @return array
     * @throws \Exception
     */
    public function fetchSpecialOperationListData(array $params = []): array
    {
        try {
            // 构建查询
            $query = $this->buildBaseQuery($params);
            
            // 获取总记录数
            $total = (clone $query)->count();
            
            // 如果没有数据，直接返回空列表
            if ($total === 0) {
                return [
                    'list' => [],
                    'total' => 0,
                    'page' => $params['page'] ?? 1,
                    'page_size' => $params['page_size'] ?? 10,
                    'from_cache' => false
                ];
            }
            
            // 添加排序
            $this->applySorting($query, $params);
            
            // 添加分页并执行查询
            $list = $query->page($params['page'] ?? 1, $params['page_size'] ?? 10)->select()->toArray();
            
            // 处理数据
            $processedList = $this->processData($list);
            
            // 构建返回数据
            return [
                'list' => $processedList,
                'total' => $total,
                'page' => $params['page'] ?? 1,
                'page_size' => $params['page_size'] ?? 10,
                'from_cache' => false
            ];
        } catch (\Throwable $e) {
            Log::error('获取特勤处数据失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * 构建基础查询
     *
     * @param array $params
     * @return \think\db\Query
     */
    private function buildBaseQuery(array $params): \think\db\Query
    {
        try {
            $query = Db::name('sjz_special_operations')
                ->where('delete_time', null)
                ->where('status', 1); // 只查询有效数据

            // 检查 lirun 字段是否存在
            $tableFields = Db::name('sjz_special_operations')->getFieldsType();
            
            $hasLirunField = isset($tableFields['lirun']);
            
            // 应用过滤条件
            $this->applyFilters($query, $params);
            
            // 准备查询字段
            $fields = [
                'id',
                'object_id',
                'name',
                'image',
                'grade',
                'place',
                'sale_price',
                'cost_price',
                'fee',
                'bail',
                'period',
                'per_count',
                'materials',
                'primary_class',
                'second_class_cn',
            ];
            
            // 根据 lirun 字段是否存在决定如何处理净利润
            if ($hasLirunField) {
                // 如果存在，直接使用
                $fields[] = 'lirun';
            } else {
                // 如果不存在，使用计算值
                $fields[] = '(sale_price - cost_price - fee) as lirun';
            }
            
            // 添加其他计算字段
            $fields[] = 'IFNULL(
                CASE 
                    WHEN per_count > 0 THEN lirun / per_count
                    ELSE 0 
                END, 0
            ) as profit_per_item';
            
            $fields[] = 'IFNULL(
                CASE 
                    WHEN period > 0 THEN lirun / period
                    ELSE 0 
                END, 0
            ) as profit_per_hour';
            
            // 设置查询字段
            $query->field($fields);
            
            return $query;
        } catch (\Throwable $e) {
            Log::error('构建查询失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * 应用过滤条件
     *
     * @param \think\db\Query $query
     * @param array $params
     * @return void
     */
    private function applyFilters(\think\db\Query $query, array $params): void
    {
        // 按位置筛选
        if (!empty($params['location'])) {
            $placeValue = $this->getPlaceValue($params['location']);
            if ($placeValue) {
                $query->where('place', $placeValue);
            }
        }

        // 按最小利润筛选
        if (isset($params['min_profit']) && $params['min_profit'] > 0) {
            $query->where('lirun', '>=', $params['min_profit']);
        }

        // 按最大周期筛选
        if (isset($params['max_cycle']) && $params['max_cycle'] > 0) {
            $query->where('period', '<=', $params['max_cycle']);
        }

        // 特殊规则：技术中心过滤掉生产周期超过16小时的项目
        $this->applySpecialRules($query, $params);
    }

    /**
     * 应用特殊过滤规则
     *
     * @param \think\db\Query $query
     * @param array $params
     * @return void
     */
    private function applySpecialRules(\think\db\Query $query, array $params): void
    {
        // 检查是否查询技术中心，或者没有指定位置（查询所有位置）
        $isQueryingTech = false;

        if (!empty($params['location'])) {
            // 如果指定了位置，检查是否是技术中心
            $placeValue = $this->getPlaceValue($params['location']);
            $isQueryingTech = ($placeValue === 'tech');
        } else {
            // 如果没有指定位置，说明要查询所有位置，需要应用技术中心的规则
            $isQueryingTech = true;
        }

        if ($isQueryingTech) {
            // 对技术中心的项目，过滤掉生产周期大于等于16小时的
            $query->where(function($subQuery) {
                $subQuery->where('place', '<>', 'tech')  // 非技术中心的项目不受限制
                         ->whereOr(function($techQuery) {
                             $techQuery->where('place', 'tech')     // 技术中心的项目
                                      ->where('period', '<', 16);   // 且周期小于16小时
                         });
            });
        }
    }

    /**
     * 获取位置值
     *
     * @param string $locationName
     * @return string|null
     */
    private function getPlaceValue(string $locationName): ?string
    {
        $placeMap = [
            '工作台' => 'workbench',
            '技术中心' => 'tech',
            '制药台' => 'pharmacy',
            '防具' => 'armory'
        ];

        return $placeMap[$locationName] ?? null;
    }

    /**
     * 应用排序
     *
     * @param \think\db\Query $query
     * @param array $params
     * @return void
     */
    private function applySorting(\think\db\Query $query, array $params): void
    {
        $sortBy = $params['sort_by'] ?? 'profit_per_hour';
        $sortOrder = $params['sort_order'] ?? 'desc';
        
        $query->order($sortBy, $sortOrder);
    }

    /**
     * 处理数据
     *
     * @param array $list
     * @return array
     */
    private function processData(array $list): array
    {
        $processedList = [];

        foreach ($list as $item) {
            $processedList[] = [
                'id' => $item['id'],
                'object_id' => $item['object_id'],
                'name' => $item['name'],
                'image' => $item['image'],
                'grade' => $item['grade'],
                'place' => $this->getPlaceDisplayName($item['place']),
                'sale_price' => (float)$item['sale_price'],
                'cost_price' => (float)$item['cost_price'],
                'fee' => (float)$item['fee'],
                'bail' => (float)$item['bail'],
                'period' => (int)$item['period'],
                'per_count' => (int)$item['per_count'],
                'materials' => $item['materials'],
                'primary_class' => $item['primary_class'],
                'second_class_cn' => $item['second_class_cn'],
                'lirun' => (float)$item['lirun'],
                'profit_per_item' => (float)$item['profit_per_item'],
                'profit_per_hour' => (float)$item['profit_per_hour']
            ];
        }

        return $processedList;
    }

    /**
     * 获取位置显示名称（英文转中文）
     *
     * @param string $placeValue
     * @return string
     */
    private function getPlaceDisplayName(string $placeValue): string
    {
        $placeMap = [
            'workbench' => '工作台',
            'tech' => '技术中心',
            'pharmacy' => '制药台',
            'armory' => '防具'
        ];

        return $placeMap[$placeValue] ?? $placeValue;
    }

    /**
     * 获取特勤处列表（带缓存）
     * 使用CacheManager进行缓存管理
     * 
     * @param array $params 查询参数
     * @return array
     * @throws \Exception
     */
    public function getSpecialOperationListWithCache(array $params = []): array
    {
        $cacheManager = new CacheManager();
        
        // 构建缓存键，包含所有查询参数
        $cacheKey = 'special_operations:list:' . md5(json_encode($params));

        // 使用 CacheManager 获取数据
        return $cacheManager->remember($cacheKey, CacheManager::TYPE_DYNAMIC_DATA, function() use ($params) {
            return $this->fetchSpecialOperationListData($params);
        });
    }

    /**
     * 获取每个位置利润最高的特勤处数据
     * 每个位置输出一条数据，按照利润排序最高的
     * 
     * @return array
     * @throws \Exception
     */
    public function fetchTopProfitByLocation(): array
    {
        try {
            // 定义所有位置（数据库中的英文值 => 显示的中文名）
            $locations = [
                'workbench' => '工作台',
                'tech' => '技术中心',
                'pharmacy' => '制药台',
                'armory' => '防具'
            ];
            $result = [];

            foreach ($locations as $placeValue => $placeName) {
                // 为每个位置查询利润最高的数据
                $query = Db::name('sjz_special_operations')
                    ->where('delete_time', null)
                    ->where('status', 1)
                    ->where('place', $placeValue);

                // 应用特殊规则：技术中心过滤掉生产周期大于等于16小时的项目
                if ($placeValue === 'tech') {
                    $query->where('period', '<', 16);
                    Log::info("SpecialOperationService: Applied tech center filter (period < 16h) for location: {$placeName}");
                }

                // 检查 lirun 字段是否存在
                $tableFields = Db::name('sjz_special_operations')->getFieldsType();
                $hasLirunField = isset($tableFields['lirun']);

                // 准备查询字段
                $fields = [
                    'id',
                    'object_id',
                    'name',
                    'image',
                    'grade',
                    'place',
                    'sale_price',
                    'cost_price',
                    'fee',
                    'bail',
                    'period',
                    'per_count',
                    'materials',
                    'primary_class',
                    'second_class_cn',
                ];

                // 根据 lirun 字段是否存在决定如何处理净利润
                if ($hasLirunField) {
                    $fields[] = 'lirun';
                } else {
                    $fields[] = '(sale_price - cost_price - fee) as lirun';
                }

                // 添加其他计算字段
                $fields[] = 'IFNULL(
                    CASE 
                        WHEN per_count > 0 THEN lirun / per_count
                        ELSE 0 
                    END, 0
                ) as profit_per_item';

                $fields[] = 'IFNULL(
                    CASE 
                        WHEN period > 0 THEN lirun / period
                        ELSE 0 
                    END, 0
                ) as profit_per_hour';

                $query->field($fields);

                // 按利润排序，获取最高利润的数据
                $topItem = $query->order('lirun', 'desc')->find();

                if ($topItem) {
                    // 处理数据
                    $processedItem = [
                        'id' => $topItem['id'],
                        'object_id' => $topItem['object_id'],
                        'name' => $topItem['name'],
                        'image' => $topItem['image'],
                        'grade' => $topItem['grade'],
                        'place' => $placeName, // 使用中文名称
                        'sale_price' => (float)$topItem['sale_price'],
                        'cost_price' => (float)$topItem['cost_price'],
                        'fee' => (float)$topItem['fee'],
                        'bail' => (float)$topItem['bail'],
                        'period' => (int)$topItem['period'],
                        'per_count' => (int)$topItem['per_count'],
                        'materials' => $topItem['materials'],
                        'primary_class' => $topItem['primary_class'],
                        'second_class_cn' => $topItem['second_class_cn'],
                        'lirun' => (float)$topItem['lirun'],
                        'profit_per_item' => (float)$topItem['profit_per_item'],
                        'profit_per_hour' => (float)$topItem['profit_per_hour']
                    ];

                    $result[] = $processedItem;
                }
            }

            // 按利润排序，确保最高利润的在前面
            usort($result, function($a, $b) {
                return $b['lirun'] <=> $a['lirun'];
            });

            return [
                'list' => $result,
                'total' => count($result),
                'page' => 1,
                'page_size' => count($result),
                'from_cache' => false,
                'type' => 'top_profit_by_location'
            ];

        } catch (\Throwable $e) {
            Log::error('获取各位置最高利润数据失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e;
        }
    }
}
