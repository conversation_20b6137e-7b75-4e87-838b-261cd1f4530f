# Chatbot 架构设计文档

## 概述

本文档详细描述了 Chatbot 模块的系统架构设计，包括整体架构、核心组件、设计模式、数据流和扩展性设计。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Chatbot API Layer                        │
├─────────────────────────────────────────────────────────────┤
│  Controller Layer (ChatbotController)                       │
│  ├── Request Processing                                     │
│  ├── Response Formatting                                    │
│  └── User/Group Management                                  │
├─────────────────────────────────────────────────────────────┤
│  Command System                                             │
│  ├── CommandFactory (Factory Pattern)                       │
│  ├── CommandInterface (Strategy Pattern)                    │
│  ├── AbstractCommand (Template Method)                      │
│  └── Core Commands (Help, Ping, Status, Version)            │
├─────────────────────────────────────────────────────────────┤
│  Plugin System                                              │
│  ├── PluginManager (Singleton + Observer)                   │
│  ├── PluginLoader (Dynamic Loading)                         │
│  ├── PluginInterface (Strategy Pattern)                     │
│  ├── AbstractPlugin (Template Method)                       │
│  └── Plugin Categories (Examples, Custom, Third-party)      │
├─────────────────────────────────────────────────────────────┤
│  Service Layer                                              │
│  ├── ChatbotDataService (Data Access)                       │
│  ├── User Management                                        │
│  ├── Group Management                                       │
│  └── Statistics Collection                                  │
├─────────────────────────────────────────────────────────────┤
│  Common Layer                                               │
│  ├── BaseController (Inheritance)                           │
│  ├── Response Formatter                                     │
│  ├── Error Handler                                          │
│  └── Logger                                                 │
└─────────────────────────────────────────────────────────────┘
```

### 分层架构

1. **API 层 (API Layer)**
   - 处理 HTTP 请求和响应
   - 参数验证和格式化
   - 路由分发

2. **控制器层 (Controller Layer)**
   - 业务逻辑协调
   - 命令和插件调度
   - 用户和群组管理

3. **命令系统 (Command System)**
   - 命令注册和管理
   - 命令执行和调度
   - 权限控制

4. **插件系统 (Plugin System)**
   - 插件生命周期管理
   - 动态加载和热插拔
   - 依赖管理

5. **服务层 (Service Layer)**
   - 数据访问和业务逻辑
   - 用户和群组数据管理
   - 统计信息收集

6. **公共层 (Common Layer)**
   - 基础类和工具
   - 错误处理和日志
   - 响应格式化

## 核心组件设计

### 1. 命令系统架构

#### 设计模式应用

**工厂模式 (Factory Pattern)**
```php
// CommandFactory.php
class CommandFactory
{
    private static array $commands = [];
    
    public static function getCommand(string $name): ?CommandInterface
    {
        if (isset(self::$commands[$name])) {
            $className = self::$commands[$name];
            return new $className();
        }
        return null;
    }
}
```

**策略模式 (Strategy Pattern)**
```php
// CommandInterface.php
interface CommandInterface
{
    public function execute(array $args, array $context): array;
    public function hasPermission(array $context): bool;
}
```

**模板方法模式 (Template Method Pattern)**
```php
// AbstractCommand.php
abstract class AbstractCommand implements CommandInterface
{
    protected function handle(array $args, array $context): array;
    
    public function execute(array $args, array $context): array
    {
        // 前置处理
        if (!$this->hasPermission($context)) {
            return $this->formatResponse(false, '权限不足', 'text');
        }
        
        // 核心处理
        $result = $this->handle($args, $context);
        
        // 后置处理
        $this->logExecution($args, $context, $result);
        
        return $result;
    }
}
```

#### 命令执行流程

```
用户输入 → 命令解析 → 权限检查 → 命令执行 → 结果格式化 → 响应返回
    ↓           ↓           ↓           ↓           ↓           ↓
ChatbotController → CommandFactory → hasPermission → handle() → formatResponse → JSON Response
```

### 2. 插件系统架构

#### 设计模式应用

**单例模式 (Singleton Pattern)**
```php
// PluginManager.php
class PluginManager
{
    private static ?PluginManager $instance = null;
    private array $plugins = [];
    
    public static function getInstance(): PluginManager
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
```

**观察者模式 (Observer Pattern)**
```php
// 插件状态变化通知
class PluginManager
{
    private array $observers = [];
    
    public function addObserver(PluginObserver $observer): void
    {
        $this->observers[] = $observer;
    }
    
    private function notifyObservers(string $event, array $data): void
    {
        foreach ($this->observers as $observer) {
            $observer->onPluginEvent($event, $data);
        }
    }
}
```

**动态加载模式 (Dynamic Loading Pattern)**
```php
// PluginLoader.php
class PluginLoader
{
    public function loadPlugin(string $pluginPath): ?PluginInterface
    {
        // 扫描插件目录
        $pluginFiles = $this->scanPluginDirectory($pluginPath);
        
        // 分析插件文件
        foreach ($pluginFiles as $file) {
            $plugin = $this->analyzePluginFile($file);
            if ($plugin) {
                return $plugin;
            }
        }
        
        return null;
    }
}
```

#### 插件生命周期

```
发现插件 → 加载插件 → 检查依赖 → 初始化插件 → 启用插件 → 运行插件 → 禁用插件 → 卸载插件
    ↓           ↓           ↓           ↓           ↓           ↓           ↓           ↓
PluginLoader → loadPlugin → checkDependencies → initialize → enable → handleCommand → disable → uninstall
```

### 3. 数据流设计

#### 请求处理流程

```
HTTP Request
    ↓
ChatbotController::process()
    ↓
参数验证和解析
    ↓
用户/群组记录处理
    ↓
命令解析 (parseCommand)
    ↓
命令执行 (processCommand)
    ├── 尝试核心命令 (CommandFactory)
    └── 尝试插件命令 (PluginManager)
    ↓
响应格式化
    ↓
HTTP Response
```

#### 数据存储设计

**用户数据模型**
```sql
CREATE TABLE chatbot_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    platform VARCHAR(50) NOT NULL,
    platform_user_id VARCHAR(100) NOT NULL,
    username VARCHAR(100),
    nickname VARCHAR(100),
    display_name VARCHAR(100),
    avatar TEXT,
    message_count INT DEFAULT 0,
    command_count INT DEFAULT 0,
    last_active TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_platform_user (platform, platform_user_id)
);
```

**群组数据模型**
```sql
CREATE TABLE chatbot_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    platform VARCHAR(50) NOT NULL,
    platform_group_id VARCHAR(100) NOT NULL,
    group_name VARCHAR(200),
    member_count INT DEFAULT 0,
    message_count INT DEFAULT 0,
    command_count INT DEFAULT 0,
    last_active TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_platform_group (platform, platform_group_id)
);
```

**插件数据模型**
```sql
CREATE TABLE chatbot_plugins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    version VARCHAR(20) NOT NULL,
    description TEXT,
    author VARCHAR(100),
    status ENUM('enabled', 'disabled', 'error') DEFAULT 'disabled',
    config JSON,
    dependencies JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_plugin_name (name)
);
```

## 扩展性设计

### 1. 命令扩展

#### 添加新命令的步骤

1. **创建命令类**
```php
class MyCommand extends AbstractCommand
{
    protected string $name = 'mycommand';
    protected array $aliases = ['mc'];
    protected string $description = '我的自定义命令';
    
    protected function handle(array $args, array $context): array
    {
        // 命令逻辑
        return $this->formatResponse(true, '命令执行成功', 'text');
    }
}
```

2. **注册命令**
```php
// 在 CommandFactory::initialize() 中添加
self::$commands['mycommand'] = MyCommand::class;
```

3. **配置权限**（可选）
```php
public function hasPermission(array $context): bool
{
    // 自定义权限逻辑
    return $context['user_role'] === 'admin';
}
```

### 2. 插件扩展

#### 添加新插件的步骤

1. **创建插件类**
```php
class MyPlugin extends AbstractPlugin
{
    protected string $name = 'myplugin';
    protected string $version = '1.0.0';
    protected string $description = '我的自定义插件';
    
    protected function onInitialize(): bool
    {
        // 初始化逻辑
        return true;
    }
    
    public function handleCommand(string $command, string $args, array $context): ?array
    {
        if ($command === 'myplugin') {
            return [
                'success' => true,
                'message' => '插件命令执行成功',
                'type' => 'text'
            ];
        }
        return null;
    }
}
```

2. **创建配置文件**
```php
// config.php
return [
    'name' => 'myplugin',
    'version' => '1.0.0',
    'description' => '我的自定义插件',
    'author' => '开发者',
    'dependencies' => [],
    'enabled' => true
];
```

3. **部署插件**
- 将插件文件放入 `plugins/custom/` 目录
- 重启服务或使用热加载功能

### 3. 接口扩展

#### 添加新API接口

1. **在控制器中添加方法**
```php
public function myApi(Request $request): void
{
    // API 逻辑
    $data = ['result' => 'success'];
    $this->success('操作成功', $data);
}
```

2. **配置路由**（在路由文件中）
```php
Route::post('/chatbot/my-api', 'ChatbotController/myApi');
```

3. **添加文档**
- 更新 API.md 文档
- 添加使用示例

## 性能优化

### 1. 缓存策略

#### 命令缓存
```php
class CommandFactory
{
    private static array $commandCache = [];
    
    public static function getCommand(string $name): ?CommandInterface
    {
        if (!isset(self::$commandCache[$name])) {
            self::$commandCache[$name] = self::createCommand($name);
        }
        return self::$commandCache[$name];
    }
}
```

#### 插件缓存
```php
class PluginManager
{
    private array $pluginCache = [];
    
    public function getPlugin(string $name): ?PluginInterface
    {
        if (!isset($this->pluginCache[$name])) {
            $this->pluginCache[$name] = $this->loadPlugin($name);
        }
        return $this->pluginCache[$name];
    }
}
```

### 2. 数据库优化

#### 索引优化
```sql
-- 用户表索引
CREATE INDEX idx_platform_user ON chatbot_users(platform, platform_user_id);
CREATE INDEX idx_last_active ON chatbot_users(last_active);

-- 群组表索引
CREATE INDEX idx_platform_group ON chatbot_groups(platform, platform_group_id);
CREATE INDEX idx_last_active ON chatbot_groups(last_active);

-- 插件表索引
CREATE INDEX idx_plugin_status ON chatbot_plugins(status);
CREATE INDEX idx_plugin_name ON chatbot_plugins(name);
```

#### 查询优化
```php
// 使用批量查询减少数据库访问
public function getUsersByPlatform(string $platform): array
{
    return $this->db->table('chatbot_users')
        ->where('platform', $platform)
        ->where('last_active', '>', date('Y-m-d H:i:s', strtotime('-7 days')))
        ->select();
}
```

### 3. 异步处理

#### 异步命令执行
```php
class AsyncCommandExecutor
{
    public function executeAsync(string $command, array $args, array $context): void
    {
        // 将命令放入队列
        $this->queue->push([
            'command' => $command,
            'args' => $args,
            'context' => $context,
            'timestamp' => time()
        ]);
    }
}
```

## 安全性设计

### 1. 输入验证

#### 参数验证
```php
class InputValidator
{
    public function validateCommandInput(array $input): array
    {
        $rules = [
            'message' => 'required|string|max:1000',
            'platform' => 'required|string|in:kook,qq,discord',
            'user_id' => 'required|string|max:100',
            'group_id' => 'string|max:100'
        ];
        
        return $this->validate($input, $rules);
    }
}
```

#### SQL注入防护
```php
// 使用参数化查询
public function getUser(string $platform, string $userId): ?array
{
    return $this->db->table('chatbot_users')
        ->where('platform', $platform)
        ->where('platform_user_id', $userId)
        ->find();
}
```

### 2. 权限控制

#### 命令权限
```php
public function hasPermission(array $context): bool
{
    $userRole = $context['user_role'] ?? 'user';
    $requiredRole = $this->getRequiredRole();
    
    return $this->checkRolePermission($userRole, $requiredRole);
}
```

#### 插件权限
```php
public function canAccessPlugin(string $pluginName, array $context): bool
{
    $plugin = $this->getPlugin($pluginName);
    if (!$plugin) {
        return false;
    }
    
    return $plugin->hasPermission($context);
}
```

### 3. 日志记录

#### 操作日志
```php
class Logger
{
    public function logCommand(string $command, array $args, array $context, array $result): void
    {
        $logData = [
            'command' => $command,
            'args' => $args,
            'user_id' => $context['user_id'] ?? '',
            'platform' => $context['platform'] ?? '',
            'success' => $result['success'] ?? false,
            'timestamp' => time()
        ];
        
        \think\facade\Log::info('Command executed', $logData);
    }
}
```

## 监控和告警

### 1. 性能监控

#### 响应时间监控
```php
class PerformanceMonitor
{
    public function monitorResponseTime(callable $callback): array
    {
        $startTime = microtime(true);
        $result = $callback();
        $endTime = microtime(true);
        
        $responseTime = $endTime - $startTime;
        
        // 记录性能指标
        $this->recordMetric('response_time', $responseTime);
        
        return $result;
    }
}
```

#### 错误率监控
```php
class ErrorMonitor
{
    public function recordError(string $type, string $message, array $context = []): void
    {
        $errorData = [
            'type' => $type,
            'message' => $message,
            'context' => $context,
            'timestamp' => time()
        ];
        
        // 记录错误
        \think\facade\Log::error('Error occurred', $errorData);
        
        // 检查是否需要告警
        $this->checkAlertThreshold($type);
    }
}
```

### 2. 健康检查

#### 系统健康检查
```php
class HealthChecker
{
    public function checkSystemHealth(): array
    {
        return [
            'database' => $this->checkDatabase(),
            'cache' => $this->checkCache(),
            'plugins' => $this->checkPlugins(),
            'commands' => $this->checkCommands(),
            'memory' => $this->checkMemory(),
            'cpu' => $this->checkCpu()
        ];
    }
}
```

## 部署架构

### 1. 单机部署

```
┌─────────────────┐
│   Web Server    │
│   (Nginx/Apache)│
├─────────────────┤
│   PHP-FPM       │
├─────────────────┤
│   Chatbot API   │
├─────────────────┤
│   MySQL         │
└─────────────────┘
```

### 2. 集群部署

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Load Balancer │    │   Load Balancer │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│   Web Server 1  │    │   Web Server 2  │    │   Web Server 3  │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│   PHP-FPM 1     │    │   PHP-FPM 2     │    │   PHP-FPM 3     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│   Chatbot API 1 │    │   Chatbot API 2 │    │   Chatbot API 3 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                └───────────────────────┘
                                        │
                                ┌─────────────────┐
                                │   MySQL Cluster │
                                │   (Master-Slave)│
                                └─────────────────┘
```

### 3. 容器化部署

```dockerfile
# Dockerfile
FROM php:8.1-fpm

# 安装依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# 安装 PHP 扩展
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# 复制应用代码
COPY . /var/www/html

# 设置权限
RUN chown -R www-data:www-data /var/www/html

# 暴露端口
EXPOSE 9000

# 启动命令
CMD ["php-fpm"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  chatbot-api:
    build: .
    ports:
      - "9000:9000"
    depends_on:
      - mysql
    environment:
      - DB_HOST=mysql
      - DB_DATABASE=chatbot
      - DB_USERNAME=root
      - DB_PASSWORD=password

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_DATABASE=chatbot
      - MYSQL_ROOT_PASSWORD=password
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

## 总结

Chatbot 模块采用分层架构设计，通过命令系统和插件系统实现了高度的模块化和扩展性。主要特点包括：

1. **模块化设计**: 命令和插件分离，便于维护和扩展
2. **设计模式应用**: 工厂模式、策略模式、模板方法模式等
3. **动态加载**: 插件支持热插拔和动态加载
4. **性能优化**: 缓存策略、数据库优化、异步处理
5. **安全性**: 输入验证、权限控制、日志记录
6. **可扩展性**: 易于添加新命令和插件
7. **监控告警**: 性能监控和错误告警机制

该架构设计确保了系统的稳定性、可维护性和可扩展性，为后续功能开发提供了良好的基础。
