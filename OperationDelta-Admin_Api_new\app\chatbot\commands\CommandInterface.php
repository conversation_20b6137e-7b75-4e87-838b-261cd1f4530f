<?php

namespace app\chatbot\commands;

/**
 * 命令处理器接口
 * 定义所有命令处理器必须实现的方法
 */
interface CommandInterface
{
    /**
     * 获取命令名称
     * @return string 命令名称（小写）
     */
    public function getName(): string;

    /**
     * 获取命令别名列表
     * @return array 命令别名数组
     */
    public function getAliases(): array;

    /**
     * 获取命令描述
     * @return string 命令描述
     */
    public function getDescription(): string;

    /**
     * 获取命令使用示例
     * @return string 使用示例
     */
    public function getUsage(): string;

    /**
     * 检查用户是否有权限执行此命令
     * @param array $context 执行上下文
     * @return bool 是否有权限
     */
    public function hasPermission(array $context): bool;

    /**
     * 执行命令
     * @param array $args 命令参数
     * @param array $context 执行上下文
     * @return array 执行结果
     */
    public function execute(array $args, array $context): array;

    /**
     * 获取命令帮助信息
     * @return string 帮助信息
     */
    public function getHelp(): string;
}
